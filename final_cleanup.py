#!/usr/bin/env python3
"""
最终清理VroidExportService.py文件
"""

def final_cleanup():
    """最终清理"""
    with open('src/service/VroidExportService.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到需要保留的部分
    new_lines = []
    skip_mode = False
    
    for i, line in enumerate(lines):
        # 如果遇到残留的BONE_PAIRS内容，开始跳过
        if ('"name": "センター"' in line or 
            '"name": "グルーブ"' in line or
            '"J_Bip_' in line and '": {' in line):
            skip_mode = True
            continue
        
        # 如果遇到RIGIDBODY_PAIRS，停止跳过
        if 'RIGIDBODY_PAIRS = {' in line:
            skip_mode = False
            new_lines.append(line)
            continue
        
        # 如果不在跳过模式，保留这行
        if not skip_mode:
            new_lines.append(line)
    
    # 写回文件
    with open('src/service/VroidExportService.py', 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"清理完成！文件从{len(lines)}行减少到{len(new_lines)}行")

if __name__ == "__main__":
    final_cleanup()
