# -*- coding: utf-8 -*-
"""
调试骨骼转换过程
"""

import sys
import os
import json
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from mmd.VroidReader import VroidReader
from service.VroidExportService import VroidExportService, get_bone_name_candidates, BONE_PAIRS

def debug_bone_mapping():
    """调试骨骼映射过程"""
    
    # UniVRM导出的VRM文件路径
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    if not os.path.exists(univrm_vrm_path):
        print(f"错误：找不到VRM文件 {univrm_vrm_path}")
        return False
    
    try:
        print("开始调试骨骼映射过程...")
        
        # 读取VRM文件
        print("正在读取VRM文件...")
        vroid_reader = VroidReader(univrm_vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model or not vrm_model.json_data:
            print("错误：无法读取VRM文件或JSON数据")
            return False
        
        json_data = vrm_model.json_data
        print(f"VRM文件读取成功，包含 {len(json_data.get('nodes', []))} 个节点")
        
        # 分析节点结构
        nodes = json_data.get('nodes', [])
        node_name_dict = {}
        
        for idx, node in enumerate(nodes):
            node_name = node.get('name', f'Node_{idx}')
            node_name_dict[node_name] = idx
        
        print(f"\n所有节点名称:")
        for i, (name, idx) in enumerate(node_name_dict.items()):
            if i < 20:  # 只显示前20个
                print(f"  [{idx:03d}] {name}")
            elif i == 20:
                print(f"  ... 还有 {len(node_name_dict) - 20} 个节点")
                break
        
        # 分析人形骨骼映射
        vrm_ext = json_data.get('extensions', {}).get('VRM', {})
        humanoid_bones = {}
        
        if 'humanoid' in vrm_ext and 'humanBones' in vrm_ext['humanoid']:
            for human_bone in vrm_ext['humanoid']['humanBones']:
                bone_name = human_bone.get('bone', '')
                node_idx = human_bone.get('node', -1)
                if node_idx >= 0 and node_idx < len(nodes):
                    node_name = nodes[node_idx].get('name', f'Node_{node_idx}')
                    humanoid_bones[bone_name] = node_name
        
        print(f"\n人形骨骼映射 ({len(humanoid_bones)} 个):")
        for bone_name, node_name in humanoid_bones.items():
            print(f"  {bone_name} -> {node_name}")
        
        # 检查BONE_PAIRS中的骨骼映射
        print(f"\n检查BONE_PAIRS中的骨骼映射:")
        found_bones = 0
        missing_bones = 0
        
        for pmx_bone_name, bone_config in BONE_PAIRS.items():
            if pmx_bone_name == "Root":
                continue
                
            # 获取候选骨骼名称
            candidates = get_bone_name_candidates(pmx_bone_name)
            found = False
            
            for candidate in candidates:
                if candidate in node_name_dict:
                    print(f"  ✓ {pmx_bone_name} -> {candidate} (找到)")
                    found = True
                    found_bones += 1
                    break
            
            if not found:
                print(f"  ✗ {pmx_bone_name} -> {candidates} (未找到)")
                missing_bones += 1
        
        print(f"\n骨骼映射统计:")
        print(f"  找到的骨骼: {found_bones}")
        print(f"  缺失的骨骼: {missing_bones}")
        print(f"  总骨骼数: {len(BONE_PAIRS) - 1}")  # 减去Root
        
        # 检查关键骨骼
        key_bones = ["J_Bip_C_Hips", "J_Bip_C_Head", "J_Bip_L_Hand", "J_Bip_R_Hand"]
        print(f"\n关键骨骼检查:")
        for key_bone in key_bones:
            candidates = get_bone_name_candidates(key_bone)
            found = False
            for candidate in candidates:
                if candidate in node_name_dict:
                    print(f"  ✓ {key_bone} -> {candidate}")
                    found = True
                    break
            if not found:
                print(f"  ✗ {key_bone} -> 未找到")
        
        # 分析缺失的重要骨骼
        important_missing = []
        for pmx_bone_name in ["J_Bip_C_Hips", "J_Bip_C_Spine", "J_Bip_C_Head", "J_Bip_C_Neck"]:
            candidates = get_bone_name_candidates(pmx_bone_name)
            found = any(candidate in node_name_dict for candidate in candidates)
            if not found:
                important_missing.append(pmx_bone_name)
        
        if important_missing:
            print(f"\n⚠️  缺失重要骨骼: {important_missing}")
            print("这可能导致转换失败")
        else:
            print(f"\n✓ 所有重要骨骼都找到了")
        
        return True
        
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_bone_pairs():
    """分析BONE_PAIRS字典"""
    print("\n分析BONE_PAIRS字典:")
    print(f"总骨骼数: {len(BONE_PAIRS)}")
    
    # 统计不同类型的骨骼
    categories = {
        "身体": ["J_Bip_C_", "Center", "Groove"],
        "手臂": ["J_Bip_L_", "J_Bip_R_", "arm_", "shoulder"],
        "腿部": ["leg_", "foot_", "toe_"],
        "手指": ["Thumb", "Index", "Middle", "Ring", "Little"],
        "其他": []
    }
    
    for bone_name in BONE_PAIRS.keys():
        categorized = False
        for category, patterns in categories.items():
            if category == "其他":
                continue
            for pattern in patterns:
                if pattern in bone_name:
                    if category not in categories:
                        categories[category] = []
                    categorized = True
                    break
            if categorized:
                break
        
        if not categorized:
            categories["其他"].append(bone_name)
    
    for category, bones in categories.items():
        if isinstance(bones, list) and bones:
            print(f"  {category}: {len(bones)} 个")
        elif not isinstance(bones, list):
            count = sum(1 for bone_name in BONE_PAIRS.keys() 
                       if any(pattern in bone_name for pattern in bones))
            print(f"  {category}: {count} 个")

if __name__ == "__main__":
    print("骨骼转换调试工具")
    print("=" * 50)
    
    # 分析BONE_PAIRS
    analyze_bone_pairs()
    
    # 调试骨骼映射
    debug_bone_mapping()
