{"exporter_version": "saturday06_blender_vrm_exporter_experimental_3.4.2", "spec_version": "0.0", "meta": {"allowedUserName": "Everyone", "author": "曹仁", "commercialUssageName": "Disallow", "contactInformation": "", "licenseName": "Redistribution_Prohibited", "otherLicenseUrl": "", "otherPermissionUrl": "", "reference": "", "sexualUssageName": "Disallow", "texture": 14, "title": "豪德寺美弥子 Bubbles", "version": "", "violentUssageName": "Disallow"}, "humanoid": {"armStretch": 0.05000000074505806, "feetSpacing": 0.0, "hasTranslationDoF": false, "humanBones": [{"bone": "hips", "node": 1, "useDefaultValues": true}, {"bone": "spine", "node": 2, "useDefaultValues": true}, {"bone": "chest", "node": 3, "useDefaultValues": true}, {"bone": "upperChest", "node": 4, "useDefaultValues": true}, {"bone": "neck", "node": 9, "useDefaultValues": true}, {"bone": "head", "node": 10, "useDefaultValues": true}, {"bone": "leftEye", "node": 11, "useDefaultValues": true}, {"bone": "rightEye", "node": 12, "useDefaultValues": true}, {"bone": "leftUpperLeg", "node": 87, "useDefaultValues": true}, {"bone": "leftLowerLeg", "node": 94, "useDefaultValues": true}, {"bone": "leftFoot", "node": 95, "useDefaultValues": true}, {"bone": "leftToes", "node": 96, "useDefaultValues": true}, {"bone": "rightUpperLeg", "node": 98, "useDefaultValues": true}, {"bone": "rightLowerLeg", "node": 105, "useDefaultValues": true}, {"bone": "rightFoot", "node": 106, "useDefaultValues": true}, {"bone": "rightToes", "node": 107, "useDefaultValues": true}, {"bone": "leftShoulder", "node": 45, "useDefaultValues": true}, {"bone": "leftUpperArm", "node": 46, "useDefaultValues": true}, {"bone": "leftLowerArm", "node": 47, "useDefaultValues": true}, {"bone": "leftHand", "node": 48, "useDefaultValues": true}, {"bone": "rightShoulder", "node": 65, "useDefaultValues": true}, {"bone": "rightUpperArm", "node": 66, "useDefaultValues": true}, {"bone": "rightLowerArm", "node": 67, "useDefaultValues": true}, {"bone": "rightHand", "node": 68, "useDefaultValues": true}, {"bone": "leftThumbProximal", "node": 61, "useDefaultValues": true}, {"bone": "leftThumbIntermediate", "node": 62, "useDefaultValues": true}, {"bone": "leftThumbDistal", "node": 63, "useDefaultValues": true}, {"bone": "leftIndexProximal", "node": 49, "useDefaultValues": true}, {"bone": "leftIndexIntermediate", "node": 50, "useDefaultValues": true}, {"bone": "leftIndexDistal", "node": 51, "useDefaultValues": true}, {"bone": "leftMiddleProximal", "node": 55, "useDefaultValues": true}, {"bone": "leftMiddleIntermediate", "node": 56, "useDefaultValues": true}, {"bone": "leftMiddleDistal", "node": 57, "useDefaultValues": true}, {"bone": "leftRingProximal", "node": 58, "useDefaultValues": true}, {"bone": "leftRingIntermediate", "node": 59, "useDefaultValues": true}, {"bone": "leftRingDistal", "node": 60, "useDefaultValues": true}, {"bone": "leftLittleProximal", "node": 52, "useDefaultValues": true}, {"bone": "leftLittleIntermediate", "node": 53, "useDefaultValues": true}, {"bone": "leftLittleDistal", "node": 54, "useDefaultValues": true}, {"bone": "rightThumbProximal", "node": 82, "useDefaultValues": true}, {"bone": "rightThumbIntermediate", "node": 83, "useDefaultValues": true}, {"bone": "rightThumbDistal", "node": 84, "useDefaultValues": true}, {"bone": "rightIndexProximal", "node": 69, "useDefaultValues": true}, {"bone": "rightIndexIntermediate", "node": 70, "useDefaultValues": true}, {"bone": "rightIndexDistal", "node": 71, "useDefaultValues": true}, {"bone": "rightMiddleProximal", "node": 75, "useDefaultValues": true}, {"bone": "rightMiddleIntermediate", "node": 76, "useDefaultValues": true}, {"bone": "rightMiddleDistal", "node": 77, "useDefaultValues": true}, {"bone": "rightRingProximal", "node": 79, "useDefaultValues": true}, {"bone": "rightRingIntermediate", "node": 80, "useDefaultValues": true}, {"bone": "rightRingDistal", "node": 81, "useDefaultValues": true}, {"bone": "rightLittleProximal", "node": 72, "useDefaultValues": true}, {"bone": "rightLittleIntermediate", "node": 73, "useDefaultValues": true}, {"bone": "rightLittleDistal", "node": 74, "useDefaultValues": true}], "legStretch": 0.05000000074505806, "lowerArmTwist": 0.5, "lowerLegTwist": 0.5, "upperArmTwist": 0.5, "upperLegTwist": 0.5}, "blend_shape_master": {"blendShapeGroups": [{"binds": [{"index": 0, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Neutral", "presetName": "neutral"}, {"binds": [{"index": 39, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "A", "presetName": "a"}, {"binds": [{"index": 40, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "I", "presetName": "i"}, {"binds": [{"index": 41, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "U", "presetName": "u"}, {"binds": [{"index": 42, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "E", "presetName": "e"}, {"binds": [{"index": 43, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "O", "presetName": "o"}, {"binds": [{"index": 13, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Blink", "presetName": "blink"}, {"binds": [{"index": 15, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Blink_L", "presetName": "blink_l"}, {"binds": [{"index": 14, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Blink_R", "presetName": "blink_r"}, {"binds": [{"index": 1, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Angry", "presetName": "angry"}, {"binds": [{"index": 2, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Fun", "presetName": "fun"}, {"binds": [{"index": 3, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "<PERSON>", "presetName": "joy"}, {"binds": [{"index": 4, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Sorrow", "presetName": "sorrow"}, {"binds": [{"index": 5, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Surprised", "presetName": "unknown"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookUp", "presetName": "lookup"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookDown", "presetName": "lookdown"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookLeft", "presetName": "lookleft"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookRight", "presetName": "lookright"}]}, "secondary_animation": {"boneGroups": [{"bones": [5, 7], "center": 0, "colliderGroups": [], "comment": "Bust", "dragForce": 0.05000000074505806, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.00016627233708277345, "stiffiness": 0.75}, {"bones": [88, 90, 92, 99, 101, 103], "center": 0, "colliderGroups": [10, 11], "comment": "Skirt", "dragForce": 0.05000000074505806, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.010646205395460129, "stiffiness": 0.5}, {"bones": [13], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.0074600474908947945, "stiffiness": 0.8500000238418579}, {"bones": [22], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.007460050750523806, "stiffiness": 0.8500000238418579}, {"bones": [31], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.0076298341155052185, "stiffiness": 0.4000000059604645}, {"bones": [34], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.007539203856140375, "stiffiness": 0.7250000238418579}, {"bones": [37], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.0075392029248178005, "stiffiness": 0.7250000238418579}, {"bones": [40], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.007629828527569771, "stiffiness": 0.4000000059604645}], "colliderGroups": [{"colliders": [{"offset": {"x": 4.440892098500626e-16, "y": 0.0, "z": -0.0}, "radius": 0.10922932624816895}], "node": 2}, {"colliders": [{"offset": {"x": -2.220446049250313e-15, "y": -2.384185791015625e-07, "z": 0.009102512151002884}, "radius": 0.09102444350719452}, {"offset": {"x": -0.045512217232177576, "y": 0.05916595458984375, "z": -0.009102409705519676}, "radius": 0.06371711194515228}, {"offset": {"x": 0.045512222549726644, "y": 0.05916595458984375, "z": -0.009102409705519676}, "radius": 0.06371711194515228}], "node": 4}, {"colliders": [{"offset": {"x": -3.552713678800501e-15, "y": 0.02730739116668701, "z": 0.01183316484093666}, "radius": 0.04551222175359726}], "node": 9}, {"colliders": [{"offset": {"x": -0.0, "y": 0.09614157676696777, "z": -0.013844376429915428}, "radius": 0.0961415097117424}], "node": 10}, {"colliders": [{"offset": {"x": 5.21540641784668e-08, "y": -0.009102463722229004, "z": -0.0}, "radius": 0.04551222728738091}, {"offset": {"x": -0.0682682916522026, "y": -0.009102463722229004, "z": 1.862645149230957e-09}, "radius": 0.04551222728738091}, {"offset": {"x": -0.1365366354584694, "y": -0.009102463722229004, "z": -1.862645149230957e-09}, "radius": 0.04551222728738091}], "node": 46}, {"colliders": [{"offset": {"x": -5.960464477539063e-08, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.02730733637242855}, {"offset": {"x": -0.045512259006500244, "y": 0.0, "z": -0.0}, "radius": 0.031858560963811865}, {"offset": {"x": -0.09102451801300049, "y": 2.384185791015625e-07, "z": 1.862645149230957e-09}, "radius": 0.02730733637242855}, {"offset": {"x": -0.13653677701950073, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.02730733637242855}], "node": 47}, {"colliders": [{"offset": {"x": -0.015798509120941162, "y": 0.0, "z": -1.862645149230957e-08}, "radius": 0.02369768870477499}], "node": 48}, {"colliders": [{"offset": {"x": -1.4901161193847656e-08, "y": -0.009102344512939453, "z": 1.862645149230957e-09}, "radius": 0.04551222547888756}, {"offset": {"x": 0.06826832890510559, "y": -0.009102344512939453, "z": 1.862645149230957e-09}, "radius": 0.04551222547888756}, {"offset": {"x": 0.13653667271137238, "y": -0.009102344512939453, "z": 3.725290298461914e-09}, "radius": 0.04551222547888756}], "node": 66}, {"colliders": [{"offset": {"x": 2.9802322387695312e-08, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.027307335287332535}, {"offset": {"x": 0.045512259006500244, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.03185855969786644}, {"offset": {"x": 0.09102451801300049, "y": 2.384185791015625e-07, "z": 1.862645149230957e-09}, "radius": 0.027307335287332535}, {"offset": {"x": 0.13653677701950073, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.027307335287332535}], "node": 67}, {"colliders": [{"offset": {"x": 0.015798628330230713, "y": -1.1920928955078125e-07, "z": -1.4901161193847656e-08}, "radius": 0.02369768684212988}], "node": 68}, {"colliders": [{"offset": {"x": 7.450580596923828e-09, "y": 5.960464477539063e-08, "z": 4.656612873077393e-10}, "radius": 0.07372977743706835}, {"offset": {"x": -0.0, "y": -0.10922932624816895, "z": -9.313225746154785e-10}, "radius": 0.07372977743706835}, {"offset": {"x": 7.450580596923828e-09, "y": -0.20025372505187988, "z": -2.3283064365386963e-10}, "radius": 0.07372977743706835}], "node": 87}, {"colliders": [{"offset": {"x": 7.450580596923828e-09, "y": 5.960464477539063e-08, "z": -1.3969838619232178e-09}, "radius": 0.07372977743706835}, {"offset": {"x": -0.0, "y": -0.10922932624816895, "z": -4.656612873077393e-10}, "radius": 0.07372977743706835}, {"offset": {"x": -0.0, "y": -0.20025372505187988, "z": -0.0}, "radius": 0.07372977743706835}], "node": 98}]}, "material_properties": [{"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 1.0, "_RimLift": 0.0, "_RimLightingMix": 0.0, "_ShadeShift": -1.1175870895385742e-08, "_ShadeToony": 0.8999999772757294, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true}, "name": "lambert6 (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_MainTex": 0}, "vectorProperties": {"_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [0.0, 0.0, 0.0, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [0.0, 0.0, 0.0, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 1.0, "_RimLift": 0.0, "_RimLightingMix": 0.0, "_ShadeShift": -1.1175870895385742e-08, "_ShadeToony": 0.8999999772757294, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true}, "name": "lambert3 (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_MainTex": 1}, "vectorProperties": {"_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [0.0, 0.0, 0.0, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [0.0, 0.0, 0.0, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_FaceMouth_00_FACE (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 2, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 10, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 5, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 0}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": true, "_ALPHAPREMULTIPLY_ON": false, "_NORMALMAP": true}, "name": "N00_000_00_EyeIris_00_EYE (Instance)", "renderQueue": 3000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Transparent"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 2, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 10, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 5, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 0}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": true, "_ALPHAPREMULTIPLY_ON": false, "_NORMALMAP": true}, "name": "N00_000_00_EyeHighlight_00_EYE (Instance)", "renderQueue": 3500, "shader": "VRM/MToon", "tagMap": {"RenderType": "Transparent"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_Face_00_SKIN (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.2833333313243593, 0.2833333313243593, 0.2833333313243593, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 16.84203338623047, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.2500009834766388, "_ShadeToony": 0.3750000149011495, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_Body_00_SKIN (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [1.0, 1.0, 1.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 16.84203338623047, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.2500009834766388, "_ShadeToony": 0.3750000149011495, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_006_01_Shoes_01_CLOTH (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [1.0, 1.0, 1.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": 1.4901161193847656e-08, "_ShadeToony": 0.6000000178813937, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_HairBack_00_HAIR (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.474509774457842, 0.7450980438916915, 0.7372549929921988, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.24999999896718345, 0.24999999896718345, 0.24999999896718345, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": 1.4901161193847656e-08, "_ShadeToony": 0.6000000178813937, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_Hair_00_HAIR_01 (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.27450970391685836, 0.09019597704651616, 0.12549006733273893, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.24999999896718345, 0.24999999896718345, 0.24999999896718345, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}]}