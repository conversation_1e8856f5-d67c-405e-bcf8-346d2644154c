#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建MORPH_PAIRS配置文件
从VroidExportService.py提取MORPH_PAIRS并创建UniVRM版本
"""

import re
import os

def extract_vroid_morph_pairs():
    """从VroidExportService.py提取VROID_MORPH_PAIRS"""
    try:
        with open('src/service/VroidExportService.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找MORPH_PAIRS定义
        pattern = r'MORPH_PAIRS\s*=\s*\{(.*?)\n\}'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            morph_content = match.group(1)
            return f"VROID_MORPH_PAIRS = {{{morph_content}\n}}"
        else:
            print("未找到MORPH_PAIRS定义")
            return None
            
    except FileNotFoundError:
        print("找不到VroidExportService.py文件")
        return None
    except Exception as e:
        print(f"提取MORPH_PAIRS时出错: {e}")
        return None

def create_univrm_morph_pairs():
    """创建UniVRM版本的MORPH_PAIRS"""
    # UniVRM主要使用标准VRM表情，VRoid特有的复杂表情可能不存在
    # 所以UniVRM版本主要包含基本的VRM标准表情
    
    univrm_morph_pairs = '''UNIVRM_MORPH_PAIRS = {
    # 基本VRM标准表情（两个平台都支持）
    "Neutral": {"name": "通常", "panel": MORPH_OTHER},
    "A": {"name": "あ", "panel": MORPH_LIP},
    "I": {"name": "い", "panel": MORPH_LIP},
    "U": {"name": "う", "panel": MORPH_LIP},
    "E": {"name": "え", "panel": MORPH_LIP},
    "O": {"name": "お", "panel": MORPH_LIP},
    "Blink": {"name": "まばたき", "panel": MORPH_EYE},
    "Joy": {"name": "喜び", "panel": MORPH_OTHER},
    "Angry": {"name": "怒り", "panel": MORPH_OTHER},
    "Sorrow": {"name": "悲しみ", "panel": MORPH_OTHER},
    "Fun": {"name": "楽しい", "panel": MORPH_OTHER},
    "LookUp": {"name": "見上げ", "panel": MORPH_EYE},
    "LookDown": {"name": "見下げ", "panel": MORPH_EYE},
    "LookLeft": {"name": "見左", "panel": MORPH_EYE},
    "LookRight": {"name": "見右", "panel": MORPH_EYE},
    "Blink_L": {"name": "ウィンク", "panel": MORPH_EYE},
    "Blink_R": {"name": "ウィンク２", "panel": MORPH_EYE},
    "Surprised": {"name": "驚き", "panel": MORPH_OTHER},
    
    # 一些可能存在的基本表情变体
    "blink": {"name": "まばたき", "panel": MORPH_EYE},  # 小写版本
    "joy": {"name": "喜び", "panel": MORPH_OTHER},
    "angry": {"name": "怒り", "panel": MORPH_OTHER},
    "sorrow": {"name": "悲しみ", "panel": MORPH_OTHER},
    "fun": {"name": "楽しい", "panel": MORPH_OTHER},
    "surprised": {"name": "驚き", "panel": MORPH_OTHER},
}'''
    
    return univrm_morph_pairs

def create_morph_pairs_config():
    """创建完整的morph_pairs.py配置文件"""
    print("提取VROID_MORPH_PAIRS...")
    vroid_content = extract_vroid_morph_pairs()
    
    if not vroid_content:
        print("提取失败，使用默认配置")
        vroid_content = "VROID_MORPH_PAIRS = {}"
    
    print("创建UNIVRM_MORPH_PAIRS...")
    univrm_content = create_univrm_morph_pairs()
    
    # 创建完整的morph_pairs.py文件
    full_content = f'''"""
VRM表情映射配置文件
包含VRoid Studio和UniVRM两种不同的表情命名规范的MORPH_PAIRS定义
"""

# 表情面板类型定义
MORPH_SYSTEM = 0
MORPH_EYEBROW = 1
MORPH_EYE = 2
MORPH_LIP = 3
MORPH_OTHER = 4

# VRoid Studio专用的MORPH_PAIRS
{vroid_content}

# UniVRM专用的MORPH_PAIRS (主要是标准VRM表情)
{univrm_content}

# 根据VRM类型选择对应的MORPH_PAIRS
def get_morph_pairs(vrm_type):
    """根据VRM导出器类型返回对应的MORPH_PAIRS"""
    if vrm_type == "UniVRM":
        return UNIVRM_MORPH_PAIRS
    else:
        return VROID_MORPH_PAIRS

# 为了向后兼容，保持原有的MORPH_PAIRS变量
MORPH_PAIRS = VROID_MORPH_PAIRS
'''
    
    # 确保config目录存在
    os.makedirs('src/config', exist_ok=True)
    
    # 写入文件
    with open('src/config/morph_pairs.py', 'w', encoding='utf-8') as f:
        f.write(full_content)
    
    print("morph_pairs.py创建完成！")
    
    # 统计表情数量
    vroid_count = len(re.findall(r'^\s*"[^"]+"\s*:\s*{', vroid_content, re.MULTILINE))
    univrm_count = len(re.findall(r'^\s*"[^"]+"\s*:\s*{', univrm_content, re.MULTILINE))
    
    print(f"VROID_MORPH_PAIRS: {vroid_count} 个表情")
    print(f"UNIVRM_MORPH_PAIRS: {univrm_count} 个表情")

def main():
    create_morph_pairs_config()

if __name__ == "__main__":
    main()
