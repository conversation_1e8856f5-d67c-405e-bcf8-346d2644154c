{"total_count": 170, "accessors": [{"index": 0, "buffer_view": 14, "byte_offset": 0, "component_type": 5126, "count": 109, "type": "MAT4", "max": null, "min": null, "normalized": false}, {"index": 1, "buffer_view": 15, "byte_offset": 0, "component_type": 5125, "count": 336, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 2, "buffer_view": 16, "byte_offset": 0, "component_type": 5126, "count": 65, "type": "VEC3", "max": [0.006033922079950571, -0.003966243006289005, 0.006033956538885832], "min": [-0.006033965386450291, -0.01603376306593418, -0.006033810321241617], "normalized": false}, {"index": 3, "buffer_view": 17, "byte_offset": 0, "component_type": 5126, "count": 65, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 4, "buffer_view": 18, "byte_offset": 0, "component_type": 5126, "count": 65, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 5, "buffer_view": 19, "byte_offset": 0, "component_type": 5125, "count": 336, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 6, "buffer_view": 20, "byte_offset": 0, "component_type": 5126, "count": 65, "type": "VEC3", "max": [0.006033919285982847, -0.003966243471950293, 0.0060339574702084064], "min": [-0.006033965386450291, -0.01603376492857933, -0.006033805664628744], "normalized": false}, {"index": 7, "buffer_view": 21, "byte_offset": 0, "component_type": 5126, "count": 65, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 8, "buffer_view": 22, "byte_offset": 0, "component_type": 5126, "count": 65, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 9, "buffer_view": 23, "byte_offset": 0, "component_type": 5125, "count": 624, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 10, "buffer_view": 24, "byte_offset": 0, "component_type": 5126, "count": 216, "type": "VEC3", "max": [-0.502722978591919, 0.04602169245481491, 0.13802209496498108], "min": [-0.5465599894523621, -0.005684384610503912, 0.05995568633079529], "normalized": false}, {"index": 11, "buffer_view": 25, "byte_offset": 0, "component_type": 5126, "count": 216, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 12, "buffer_view": 26, "byte_offset": 0, "component_type": 5126, "count": 216, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 13, "buffer_view": 27, "byte_offset": 0, "component_type": 5125, "count": 822, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 14, "buffer_view": 28, "byte_offset": 0, "component_type": 5126, "count": 232, "type": "VEC3", "max": [0.00747503200545907, 0.011600970290601254, 0.007474957033991814], "min": [-0.007475076708942652, -0.011600970290601254, -0.00747494725510478], "normalized": false}, {"index": 15, "buffer_view": 29, "byte_offset": 0, "component_type": 5126, "count": 232, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 16, "buffer_view": 30, "byte_offset": 0, "component_type": 5126, "count": 232, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 17, "buffer_view": 31, "byte_offset": 0, "component_type": 5125, "count": 624, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 18, "buffer_view": 32, "byte_offset": 0, "component_type": 5126, "count": 216, "type": "VEC3", "max": [0.5465602278709412, 0.0460214763879776, 0.13802219927310944], "min": [0.5027232766151428, -0.005684613715857267, 0.05995575711131096], "normalized": false}, {"index": 19, "buffer_view": 33, "byte_offset": 0, "component_type": 5126, "count": 216, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 20, "buffer_view": 34, "byte_offset": 0, "component_type": 5126, "count": 216, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 21, "buffer_view": 35, "byte_offset": 0, "component_type": 5125, "count": 6522, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 22, "buffer_view": 36, "byte_offset": 0, "component_type": 5126, "count": 1788, "type": "VEC3", "max": [0.11215076595544815, 0.030593357980251312, 0.10801360011100769], "min": [-0.11215077340602875, -0.03058851882815361, -0.10801410675048828], "normalized": false}, {"index": 23, "buffer_view": 37, "byte_offset": 0, "component_type": 5126, "count": 1788, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 24, "buffer_view": 38, "byte_offset": 0, "component_type": 5126, "count": 1788, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 25, "buffer_view": 39, "byte_offset": 0, "component_type": 5125, "count": 864, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 26, "buffer_view": 40, "byte_offset": 0, "component_type": 5126, "count": 250, "type": "VEC3", "max": [0.023332592099905014, 0.0035351819824427366, 0.0408029705286026], "min": [-0.0683109238743782, -0.03825492039322853, -0.057673607021570206], "normalized": false}, {"index": 27, "buffer_view": 41, "byte_offset": 0, "component_type": 5126, "count": 250, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 28, "buffer_view": 42, "byte_offset": 0, "component_type": 5126, "count": 250, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 29, "buffer_view": 43, "byte_offset": 0, "component_type": 5125, "count": 864, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 30, "buffer_view": 44, "byte_offset": 0, "component_type": 5126, "count": 250, "type": "VEC3", "max": [0.04582175984978676, 0.02089504897594452, 0.049238286912441254], "min": [-0.04582173377275467, -0.020895054563879967, -0.04923829436302185], "normalized": false}, {"index": 31, "buffer_view": 45, "byte_offset": 0, "component_type": 5126, "count": 250, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 32, "buffer_view": 46, "byte_offset": 0, "component_type": 5126, "count": 250, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 33, "buffer_view": 47, "byte_offset": 0, "component_type": 5125, "count": 6672, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 34, "buffer_view": 48, "byte_offset": 0, "component_type": 5125, "count": 1146, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 35, "buffer_view": 49, "byte_offset": 0, "component_type": 5125, "count": 534, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 36, "buffer_view": 50, "byte_offset": 0, "component_type": 5125, "count": 12696, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 37, "buffer_view": 51, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0963834822177887, 1.4481312036514282, 0.0422368086874485], "min": [-0.09638345241546631, 1.2231502532958984, -0.08267940580844879], "normalized": false}, {"index": 38, "buffer_view": 52, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 39, "buffer_view": 53, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 40, "buffer_view": 54, "byte_offset": 0, "component_type": 5123, "count": 3998, "type": "VEC4", "max": null, "min": null, "normalized": false}, {"index": 41, "buffer_view": 55, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC4", "max": null, "min": null, "normalized": false}, {"index": 42, "buffer_view": 56, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.001122710877098143, 0.0008530616760253906, 0.0031089731492102146], "min": [-0.001122712274082005, -0.001661539077758789, -0.0003454163670539856], "normalized": false}, {"index": 43, "buffer_view": 57, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 44, "buffer_view": 58, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.005554173141717911, 0.0023888349533081055, 0.0031094527803361416], "min": [-0.0055540744215250015, -0.017661333084106445, -0.001421116292476654], "normalized": false}, {"index": 45, "buffer_view": 59, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 46, "buffer_view": 60, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.012424433836713433, 0.011666536331176758, 0.007022264413535595], "min": [-0.012424430809915066, -0.004978775978088379, -0.0022277161478996277], "normalized": false}, {"index": 47, "buffer_view": 61, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 48, "buffer_view": 62, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.011312467977404594, 0.021725177764892578, 0.016753770411014557], "min": [-0.01131246518343687, -0.023126602172851562, -0.002363339066505432], "normalized": false}, {"index": 49, "buffer_view": 63, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 50, "buffer_view": 64, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.005696137435734272, 0.0045032501220703125, 0.006836690939962864], "min": [-0.005696136504411697, -0.016470909118652344, -0.00233333557844162], "normalized": false}, {"index": 51, "buffer_view": 65, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 52, "buffer_view": 66, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.008117489516735077, 0.010694265365600586, 0.017030246555805206], "min": [-0.008117482997477055, -0.01832294464111328, -0.003486134111881256], "normalized": false}, {"index": 53, "buffer_view": 67, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 54, "buffer_view": 68, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.005554173141717911, 1.2159347534179688e-05, 0.0022484660148620605], "min": [-0.0055540744215250015, -0.017661333084106445, -3.1068921089172363e-06], "normalized": false}, {"index": 55, "buffer_view": 69, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 56, "buffer_view": 70, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003870489075779915, 0.011666536331176758, 0.00017414987087249756], "min": [-0.0038705039769411087, 0.0, -0.0008011087775230408], "normalized": false}, {"index": 57, "buffer_view": 71, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 58, "buffer_view": 72, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0011532194912433624, 0.006557345390319824, 0.00017414987087249756], "min": [-0.001153191551566124, 0.0, -0.000925406813621521], "normalized": false}, {"index": 59, "buffer_view": 73, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 60, "buffer_view": 74, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003948904573917389, 1.2159347534179688e-05, 0.002801842987537384], "min": [-0.003948885947465897, -0.016470909118652344, 0.0], "normalized": false}, {"index": 61, "buffer_view": 75, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 62, "buffer_view": 76, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003899727016687393, 0.010694265365600586, 0.0008007846772670746], "min": [-0.0038997288793325424, 0.0, -0.00037595629692077637], "normalized": false}, {"index": 63, "buffer_view": 77, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 64, "buffer_view": 78, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [-0.0, 0.0, 0.0], "min": [-0.0, 0.0, 0.0], "normalized": false}, {"index": 65, "buffer_view": 79, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 66, "buffer_view": 80, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0008793063461780548, 5.960464477539062e-07, 0.0011743083596229553], "min": [-0.0008793175220489502, -0.008043885231018066, -3.395974636077881e-05], "normalized": false}, {"index": 67, "buffer_view": 81, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 68, "buffer_view": 82, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.005547963082790375, 0.0041158199310302734, 0.005805846303701401], "min": [-0.005547970533370972, -0.03826117515563965, -0.0007411390542984009], "normalized": false}, {"index": 69, "buffer_view": 83, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 70, "buffer_view": 84, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.005547963082790375, 0.0041158199310302734, 0.005805831402540207], "min": [-0.004162319004535675, -0.0382610559463501, -0.0007411390542984009], "normalized": false}, {"index": 71, "buffer_view": 85, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 72, "buffer_view": 86, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.004162326455116272, 0.0041158199310302734, 0.005805846303701401], "min": [-0.005547970533370972, -0.03826117515563965, -0.0007411390542984009], "normalized": false}, {"index": 73, "buffer_view": 87, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 74, "buffer_view": 88, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0019125305116176605, 0.006443381309509277, 0.0010890364646911621], "min": [-0.001912541687488556, -0.004978775978088379, -0.0008060038089752197], "normalized": false}, {"index": 75, "buffer_view": 89, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 76, "buffer_view": 90, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003619108349084854, 0.021725177764892578, 0.0036808475852012634], "min": [-0.0036190710961818695, -0.023126602172851562, -0.002363339066505432], "normalized": false}, {"index": 77, "buffer_view": 91, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 78, "buffer_view": 92, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0021110177040100098, 0.021725058555603027, 0.0036808475852012634], "min": [-0.0036190710961818695, -0.02312636375427246, -0.002363339066505432], "normalized": false}, {"index": 79, "buffer_view": 93, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 80, "buffer_view": 94, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003619108349084854, 0.021725177764892578, 0.0036808475852012634], "min": [-0.0021594054996967316, -0.023126602172851562, -0.002363339066505432], "normalized": false}, {"index": 81, "buffer_view": 95, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 82, "buffer_view": 96, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0011388659477233887, 0.0045032501220703125, 0.0010513067245483398], "min": [-0.0011388473212718964, -0.009489655494689941, -0.0005337148904800415], "normalized": false}, {"index": 83, "buffer_view": 97, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 84, "buffer_view": 98, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.00664285384118557, 0.00695037841796875, 0.002797383815050125], "min": [-0.006642866879701614, -0.006145477294921875, -0.0026607364416122437], "normalized": false}, {"index": 85, "buffer_view": 99, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 86, "buffer_view": 100, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0005191788077354431, 0.00756525993347168, 6.92903995513916e-05], "min": [-0.0005191750824451447, 0.0, -0.0004272833466529846], "normalized": false}, {"index": 87, "buffer_view": 101, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 88, "buffer_view": 102, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.015572324395179749, 0.01628279685974121, 0.04207590967416763], "min": [-0.010567821562290192, -0.008708477020263672, 0.0], "normalized": false}, {"index": 89, "buffer_view": 103, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 90, "buffer_view": 104, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [5.316734313964844e-05, 1.2159347534179688e-05, 0.00017414987087249756], "min": [-5.317479372024536e-05, 0.0, 0.0], "normalized": false}, {"index": 91, "buffer_view": 105, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 92, "buffer_view": 106, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [6.0467980802059174e-05, 0.0006300210952758789, 0.00017414987087249756], "min": [-6.0463324189186096e-05, 0.0, -0.00012720376253128052], "normalized": false}, {"index": 93, "buffer_view": 107, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 94, "buffer_view": 108, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003687262535095215, 0.008519291877746582, 0.001850702567026019], "min": [-0.003687262535095215, -0.000247955322265625, -0.0040158964693546295], "normalized": false}, {"index": 95, "buffer_view": 109, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 96, "buffer_view": 110, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0027583595365285873, 9.298324584960938e-06, 0.009779817890375853], "min": [-0.0027583595365285873, -0.014424681663513184, -0.000738099217414856], "normalized": false}, {"index": 97, "buffer_view": 111, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 98, "buffer_view": 112, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.004399536177515984, 0.0023888349533081055, 0.0031094527803361416], "min": [-0.004399539902806282, -0.004936337471008301, -0.001421116292476654], "normalized": false}, {"index": 99, "buffer_view": 113, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 100, "buffer_view": 114, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.010100531857460737, 0.0005207061767578125, 0.0], "min": [-0.010100529063493013, -0.0007703304290771484, -0.004846344469115138], "normalized": false}, {"index": 101, "buffer_view": 115, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 102, "buffer_view": 116, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.022139687789604068, 0.0007734298706054688, 0.012192064663395286], "min": [-0.02213968290016055, -0.0006520748138427734, -3.0472874641418457e-05], "normalized": false}, {"index": 103, "buffer_view": 117, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 104, "buffer_view": 118, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.001122710877098143, 0.0008530616760253906, 0.0031089731492102146], "min": [-0.001122712274082005, -0.001661539077758789, -0.0003454163670539856], "normalized": false}, {"index": 105, "buffer_view": 119, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 106, "buffer_view": 120, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.012424433836713433, 0.00883495807647705, 0.007022264413535595], "min": [-0.012424430809915066, 0.0, -0.0022277161478996277], "normalized": false}, {"index": 107, "buffer_view": 121, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 108, "buffer_view": 122, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.011312467977404594, 0.010434269905090332, 0.016753770411014557], "min": [-0.01131246518343687, -0.017862677574157715, -0.00177060067653656], "normalized": false}, {"index": 109, "buffer_view": 123, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 110, "buffer_view": 124, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.005696137435734272, 0.0023604631423950195, 0.006836690939962864], "min": [-0.005696136504411697, -0.01256406307220459, -0.00233333557844162], "normalized": false}, {"index": 111, "buffer_view": 125, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 112, "buffer_view": 126, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.008117489516735077, 0.009595036506652832, 0.017030246555805206], "min": [-0.008117482997477055, -0.01832294464111328, -0.003486134111881256], "normalized": false}, {"index": 113, "buffer_view": 127, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 114, "buffer_view": 128, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0014139795675873756, 1.1920928955078125e-07, 0.0003575459122657776], "min": [-0.0014139842242002487, -0.004284381866455078, -0.0007310360670089722], "normalized": false}, {"index": 115, "buffer_view": 129, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 116, "buffer_view": 130, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [1.329771475866437e-05, 1.1920928955078125e-07, 0.00035753846168518066], "min": [-0.0014139842242002487, -0.004284262657165527, -0.0007310360670089722], "normalized": false}, {"index": 117, "buffer_view": 131, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 118, "buffer_view": 132, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0014139795675873756, 1.1920928955078125e-07, 0.0003575459122657776], "min": [-2.987682819366455e-06, -0.004284381866455078, -0.0007310360670089722], "normalized": false}, {"index": 119, "buffer_view": 133, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 120, "buffer_view": 134, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.003578372299671173, 0.0071866512298583984, 0.007621202617883682], "min": [-0.0035783685743808746, -0.010481834411621094, -0.00277593731880188], "normalized": false}, {"index": 121, "buffer_view": 135, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 122, "buffer_view": 136, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.007976492634043097, 0.0017406940460205078, 0.005827129352837801], "min": [-0.007976488675922155, -0.0030362606048583984, -0.001231461763381958], "normalized": false}, {"index": 123, "buffer_view": 137, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 124, "buffer_view": 138, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.010625751223415136, 0.001822829246520996, 0.003585533704608679], "min": [-0.010625748429447412, -0.008756041526794434, -0.006524108350276947], "normalized": false}, {"index": 125, "buffer_view": 139, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 126, "buffer_view": 140, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.011383120203390718, 0.002997279167175293, 0.00892995111644268], "min": [-0.01138311717659235, -0.005559206008911133, -0.0005019232630729675], "normalized": false}, {"index": 127, "buffer_view": 141, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 128, "buffer_view": 142, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.010200009681284428, 0.007272481918334961, 0.017274343641474843], "min": [-0.0102000143378973, -0.015123724937438965, -0.000805780291557312], "normalized": false}, {"index": 129, "buffer_view": 143, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 130, "buffer_view": 144, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.01943912450224161, 0.023317456245422363, 0.06851199269294739], "min": [-0.019439107505604625, 0.0, 0.0], "normalized": false}, {"index": 131, "buffer_view": 145, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 132, "buffer_view": 146, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0017263321788050234, 0.004126429557800293, 0.0008871335157891735], "min": [-0.0017263315967284143, -0.005475759506225586, -0.0020099154444324085], "normalized": false}, {"index": 133, "buffer_view": 147, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 134, "buffer_view": 148, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0017263321788050234, 0.004126429557800293, 0.000577686121687293], "min": [-0.0017263315967284143, -0.00018906593322753906, -0.0020099154444324085], "normalized": false}, {"index": 135, "buffer_view": 149, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 136, "buffer_view": 150, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0010979195358231664, 0.00029671192169189453, 0.0008871335157891735], "min": [-0.0010979200014844537, -0.005475759506225586, -0.0016770483925938606], "normalized": false}, {"index": 137, "buffer_view": 151, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 138, "buffer_view": 152, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0025678056990727782, 0.005202531814575195, 0.0017114418296841905], "min": [-0.002567805931903422, -0.005460262298583984, -0.00450711720623076], "normalized": false}, {"index": 139, "buffer_view": 153, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 140, "buffer_view": 154, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.002286141214426607, 0.005202531814575195, 0.0013787157367914915], "min": [-0.0022861408651806414, -0.00016105175018310547, -0.00450711720623076], "normalized": false}, {"index": 141, "buffer_view": 155, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 142, "buffer_view": 156, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0025678056990727782, 0.00029671192169189453, 0.001711438104393892], "min": [-0.002567805931903422, -0.005460262298583984, -0.0016770483925938606], "normalized": false}, {"index": 143, "buffer_view": 157, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 144, "buffer_view": 158, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.001659020024817437, 0.005232334136962891, 0.0006714516202919185], "min": [-0.0016590202576480806, -0.004348039627075195, -0.0010896475287154317], "normalized": false}, {"index": 145, "buffer_view": 159, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 146, "buffer_view": 160, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.0016049263067543507, 0.0, 0.0006714516202919185], "min": [-0.001604926073923707, -0.004348039627075195, 0.0], "normalized": false}, {"index": 147, "buffer_view": 161, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 148, "buffer_view": 162, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [0.001659020024817437, 0.005232334136962891, 7.521361112594604e-05], "min": [-0.0016590202576480806, -1.3113021850585938e-06, -0.0010896475287154317], "normalized": false}, {"index": 149, "buffer_view": 163, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 150, "buffer_view": 164, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [6.640112906097784e-05, 0.0048906803131103516, 0.0016923223156481981], "min": [-6.64015192342049e-05, -0.004753589630126953, -0.0016162584797712043], "normalized": false}, {"index": 151, "buffer_view": 165, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 152, "buffer_view": 166, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [6.640112906097784e-05, 0.0048906803131103516, 0.0], "min": [-6.64015192342049e-05, 0.0, -0.0016162584797712043], "normalized": false}, {"index": 153, "buffer_view": 167, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 154, "buffer_view": 168, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": [5.804962711408734e-05, 0.0, 0.0016923223156481981], "min": [-5.8046134654432535e-05, -0.004763364791870117, 0.0], "normalized": false}, {"index": 155, "buffer_view": 169, "byte_offset": 0, "component_type": 5126, "count": 3998, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 156, "buffer_view": 170, "byte_offset": 0, "component_type": 5125, "count": 30066, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 157, "buffer_view": 171, "byte_offset": 0, "component_type": 5125, "count": 12774, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 158, "buffer_view": 172, "byte_offset": 0, "component_type": 5125, "count": 4182, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 159, "buffer_view": 173, "byte_offset": 0, "component_type": 5126, "count": 9473, "type": "VEC3", "max": [0.6556846499443054, 1.4601482152938843, 0.14574286341667175], "min": [-0.6556845903396606, -2.541938215472328e-07, -0.13017471134662628], "normalized": false}, {"index": 160, "buffer_view": 174, "byte_offset": 0, "component_type": 5126, "count": 9473, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 161, "buffer_view": 175, "byte_offset": 0, "component_type": 5126, "count": 9473, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 162, "buffer_view": 176, "byte_offset": 0, "component_type": 5123, "count": 9473, "type": "VEC4", "max": null, "min": null, "normalized": false}, {"index": 163, "buffer_view": 177, "byte_offset": 0, "component_type": 5126, "count": 9473, "type": "VEC4", "max": null, "min": null, "normalized": false}, {"index": 164, "buffer_view": 178, "byte_offset": 0, "component_type": 5125, "count": 62244, "type": "SCALAR", "max": null, "min": null, "normalized": false}, {"index": 165, "buffer_view": 179, "byte_offset": 0, "component_type": 5126, "count": 16492, "type": "VEC3", "max": [0.22542279958724976, 1.496543049812317, 0.1547117829322815], "min": [-0.22542275488376617, 1.2286665439605713, -0.08605095744132996], "normalized": false}, {"index": 166, "buffer_view": 180, "byte_offset": 0, "component_type": 5126, "count": 16492, "type": "VEC3", "max": null, "min": null, "normalized": false}, {"index": 167, "buffer_view": 181, "byte_offset": 0, "component_type": 5126, "count": 16492, "type": "VEC2", "max": null, "min": null, "normalized": false}, {"index": 168, "buffer_view": 182, "byte_offset": 0, "component_type": 5123, "count": 16492, "type": "VEC4", "max": null, "min": null, "normalized": false}, {"index": 169, "buffer_view": 183, "byte_offset": 0, "component_type": 5126, "count": 16492, "type": "VEC4", "max": null, "min": null, "normalized": false}]}