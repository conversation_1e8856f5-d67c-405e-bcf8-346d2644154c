{"accessors": [{"bufferView": 14, "byteOffset": 0, "componentType": 5126, "count": 109, "type": "MAT4"}, {"bufferView": 15, "byteOffset": 0, "componentType": 5125, "count": 336, "type": "SCALAR"}, {"bufferView": 16, "byteOffset": 0, "componentType": 5126, "count": 65, "max": [0.006033922079950571, -0.003966243006289005, 0.006033956538885832], "min": [-0.006033965386450291, -0.01603376306593418, -0.006033810321241617], "type": "VEC3"}, {"bufferView": 17, "byteOffset": 0, "componentType": 5126, "count": 65, "type": "VEC3"}, {"bufferView": 18, "byteOffset": 0, "componentType": 5126, "count": 65, "type": "VEC2"}, {"bufferView": 19, "byteOffset": 0, "componentType": 5125, "count": 336, "type": "SCALAR"}, {"bufferView": 20, "byteOffset": 0, "componentType": 5126, "count": 65, "max": [0.006033919285982847, -0.003966243471950293, 0.0060339574702084064], "min": [-0.006033965386450291, -0.01603376492857933, -0.006033805664628744], "type": "VEC3"}, {"bufferView": 21, "byteOffset": 0, "componentType": 5126, "count": 65, "type": "VEC3"}, {"bufferView": 22, "byteOffset": 0, "componentType": 5126, "count": 65, "type": "VEC2"}, {"bufferView": 23, "byteOffset": 0, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 24, "byteOffset": 0, "componentType": 5126, "count": 216, "max": [-0.502722978591919, 0.04602169245481491, 0.13802209496498108], "min": [-0.5465599894523621, -0.005684384610503912, 0.05995568633079529], "type": "VEC3"}, {"bufferView": 25, "byteOffset": 0, "componentType": 5126, "count": 216, "type": "VEC3"}, {"bufferView": 26, "byteOffset": 0, "componentType": 5126, "count": 216, "type": "VEC2"}, {"bufferView": 27, "byteOffset": 0, "componentType": 5125, "count": 822, "type": "SCALAR"}, {"bufferView": 28, "byteOffset": 0, "componentType": 5126, "count": 232, "max": [0.00747503200545907, 0.011600970290601254, 0.007474957033991814], "min": [-0.007475076708942652, -0.011600970290601254, -0.00747494725510478], "type": "VEC3"}, {"bufferView": 29, "byteOffset": 0, "componentType": 5126, "count": 232, "type": "VEC3"}, {"bufferView": 30, "byteOffset": 0, "componentType": 5126, "count": 232, "type": "VEC2"}, {"bufferView": 31, "byteOffset": 0, "componentType": 5125, "count": 624, "type": "SCALAR"}, {"bufferView": 32, "byteOffset": 0, "componentType": 5126, "count": 216, "max": [0.5465602278709412, 0.0460214763879776, 0.13802219927310944], "min": [0.5027232766151428, -0.005684613715857267, 0.05995575711131096], "type": "VEC3"}, {"bufferView": 33, "byteOffset": 0, "componentType": 5126, "count": 216, "type": "VEC3"}, {"bufferView": 34, "byteOffset": 0, "componentType": 5126, "count": 216, "type": "VEC2"}, {"bufferView": 35, "byteOffset": 0, "componentType": 5125, "count": 6522, "type": "SCALAR"}, {"bufferView": 36, "byteOffset": 0, "componentType": 5126, "count": 1788, "max": [0.11215076595544815, 0.030593357980251312, 0.10801360011100769], "min": [-0.11215077340602875, -0.03058851882815361, -0.10801410675048828], "type": "VEC3"}, {"bufferView": 37, "byteOffset": 0, "componentType": 5126, "count": 1788, "type": "VEC3"}, {"bufferView": 38, "byteOffset": 0, "componentType": 5126, "count": 1788, "type": "VEC2"}, {"bufferView": 39, "byteOffset": 0, "componentType": 5125, "count": 864, "type": "SCALAR"}, {"bufferView": 40, "byteOffset": 0, "componentType": 5126, "count": 250, "max": [0.023332592099905014, 0.0035351819824427366, 0.0408029705286026], "min": [-0.0683109238743782, -0.03825492039322853, -0.057673607021570206], "type": "VEC3"}, {"bufferView": 41, "byteOffset": 0, "componentType": 5126, "count": 250, "type": "VEC3"}, {"bufferView": 42, "byteOffset": 0, "componentType": 5126, "count": 250, "type": "VEC2"}, {"bufferView": 43, "byteOffset": 0, "componentType": 5125, "count": 864, "type": "SCALAR"}, {"bufferView": 44, "byteOffset": 0, "componentType": 5126, "count": 250, "max": [0.04582175984978676, 0.02089504897594452, 0.049238286912441254], "min": [-0.04582173377275467, -0.020895054563879967, -0.04923829436302185], "type": "VEC3"}, {"bufferView": 45, "byteOffset": 0, "componentType": 5126, "count": 250, "type": "VEC3"}, {"bufferView": 46, "byteOffset": 0, "componentType": 5126, "count": 250, "type": "VEC2"}, {"bufferView": 47, "byteOffset": 0, "componentType": 5125, "count": 6672, "type": "SCALAR"}, {"bufferView": 48, "byteOffset": 0, "componentType": 5125, "count": 1146, "type": "SCALAR"}, {"bufferView": 49, "byteOffset": 0, "componentType": 5125, "count": 534, "type": "SCALAR"}, {"bufferView": 50, "byteOffset": 0, "componentType": 5125, "count": 12696, "type": "SCALAR"}, {"bufferView": 51, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0963834822177887, 1.4481312036514282, 0.0422368086874485], "min": [-0.09638345241546631, 1.2231502532958984, -0.08267940580844879], "type": "VEC3"}, {"bufferView": 52, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 53, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC2"}, {"bufferView": 54, "byteOffset": 0, "componentType": 5123, "count": 3998, "type": "VEC4"}, {"bufferView": 55, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC4"}, {"bufferView": 56, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.001122710877098143, 0.0008530616760253906, 0.0031089731492102146], "min": [-0.001122712274082005, -0.001661539077758789, -0.0003454163670539856], "type": "VEC3"}, {"bufferView": 57, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 58, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.005554173141717911, 0.0023888349533081055, 0.0031094527803361416], "min": [-0.0055540744215250015, -0.017661333084106445, -0.001421116292476654], "type": "VEC3"}, {"bufferView": 59, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 60, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.012424433836713433, 0.011666536331176758, 0.007022264413535595], "min": [-0.012424430809915066, -0.004978775978088379, -0.0022277161478996277], "type": "VEC3"}, {"bufferView": 61, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 62, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.011312467977404594, 0.021725177764892578, 0.016753770411014557], "min": [-0.01131246518343687, -0.023126602172851562, -0.002363339066505432], "type": "VEC3"}, {"bufferView": 63, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 64, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.005696137435734272, 0.0045032501220703125, 0.006836690939962864], "min": [-0.005696136504411697, -0.016470909118652344, -0.00233333557844162], "type": "VEC3"}, {"bufferView": 65, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 66, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.008117489516735077, 0.010694265365600586, 0.017030246555805206], "min": [-0.008117482997477055, -0.01832294464111328, -0.003486134111881256], "type": "VEC3"}, {"bufferView": 67, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 68, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.005554173141717911, 1.2159347534179688e-05, 0.0022484660148620605], "min": [-0.0055540744215250015, -0.017661333084106445, -3.1068921089172363e-06], "type": "VEC3"}, {"bufferView": 69, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 70, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003870489075779915, 0.011666536331176758, 0.00017414987087249756], "min": [-0.0038705039769411087, 0.0, -0.0008011087775230408], "type": "VEC3"}, {"bufferView": 71, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 72, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0011532194912433624, 0.006557345390319824, 0.00017414987087249756], "min": [-0.001153191551566124, 0.0, -0.000925406813621521], "type": "VEC3"}, {"bufferView": 73, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 74, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003948904573917389, 1.2159347534179688e-05, 0.002801842987537384], "min": [-0.003948885947465897, -0.016470909118652344, 0.0], "type": "VEC3"}, {"bufferView": 75, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 76, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003899727016687393, 0.010694265365600586, 0.0008007846772670746], "min": [-0.0038997288793325424, 0.0, -0.00037595629692077637], "type": "VEC3"}, {"bufferView": 77, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 78, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [-0.0, 0.0, 0.0], "min": [-0.0, 0.0, 0.0], "type": "VEC3"}, {"bufferView": 79, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 80, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0008793063461780548, 5.960464477539062e-07, 0.0011743083596229553], "min": [-0.0008793175220489502, -0.008043885231018066, -3.395974636077881e-05], "type": "VEC3"}, {"bufferView": 81, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 82, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.005547963082790375, 0.0041158199310302734, 0.005805846303701401], "min": [-0.005547970533370972, -0.03826117515563965, -0.0007411390542984009], "type": "VEC3"}, {"bufferView": 83, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 84, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.005547963082790375, 0.0041158199310302734, 0.005805831402540207], "min": [-0.004162319004535675, -0.0382610559463501, -0.0007411390542984009], "type": "VEC3"}, {"bufferView": 85, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 86, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.004162326455116272, 0.0041158199310302734, 0.005805846303701401], "min": [-0.005547970533370972, -0.03826117515563965, -0.0007411390542984009], "type": "VEC3"}, {"bufferView": 87, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 88, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0019125305116176605, 0.006443381309509277, 0.0010890364646911621], "min": [-0.001912541687488556, -0.004978775978088379, -0.0008060038089752197], "type": "VEC3"}, {"bufferView": 89, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 90, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003619108349084854, 0.021725177764892578, 0.0036808475852012634], "min": [-0.0036190710961818695, -0.023126602172851562, -0.002363339066505432], "type": "VEC3"}, {"bufferView": 91, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 92, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0021110177040100098, 0.021725058555603027, 0.0036808475852012634], "min": [-0.0036190710961818695, -0.02312636375427246, -0.002363339066505432], "type": "VEC3"}, {"bufferView": 93, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 94, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003619108349084854, 0.021725177764892578, 0.0036808475852012634], "min": [-0.0021594054996967316, -0.023126602172851562, -0.002363339066505432], "type": "VEC3"}, {"bufferView": 95, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 96, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0011388659477233887, 0.0045032501220703125, 0.0010513067245483398], "min": [-0.0011388473212718964, -0.009489655494689941, -0.0005337148904800415], "type": "VEC3"}, {"bufferView": 97, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 98, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.00664285384118557, 0.00695037841796875, 0.002797383815050125], "min": [-0.006642866879701614, -0.006145477294921875, -0.0026607364416122437], "type": "VEC3"}, {"bufferView": 99, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 100, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0005191788077354431, 0.00756525993347168, 6.92903995513916e-05], "min": [-0.0005191750824451447, 0.0, -0.0004272833466529846], "type": "VEC3"}, {"bufferView": 101, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 102, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.015572324395179749, 0.01628279685974121, 0.04207590967416763], "min": [-0.010567821562290192, -0.008708477020263672, 0.0], "type": "VEC3"}, {"bufferView": 103, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 104, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [5.316734313964844e-05, 1.2159347534179688e-05, 0.00017414987087249756], "min": [-5.317479372024536e-05, 0.0, 0.0], "type": "VEC3"}, {"bufferView": 105, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 106, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [6.0467980802059174e-05, 0.0006300210952758789, 0.00017414987087249756], "min": [-6.0463324189186096e-05, 0.0, -0.00012720376253128052], "type": "VEC3"}, {"bufferView": 107, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 108, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003687262535095215, 0.008519291877746582, 0.001850702567026019], "min": [-0.003687262535095215, -0.000247955322265625, -0.0040158964693546295], "type": "VEC3"}, {"bufferView": 109, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 110, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0027583595365285873, 9.298324584960938e-06, 0.009779817890375853], "min": [-0.0027583595365285873, -0.014424681663513184, -0.000738099217414856], "type": "VEC3"}, {"bufferView": 111, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 112, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.004399536177515984, 0.0023888349533081055, 0.0031094527803361416], "min": [-0.004399539902806282, -0.004936337471008301, -0.001421116292476654], "type": "VEC3"}, {"bufferView": 113, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 114, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.010100531857460737, 0.0005207061767578125, 0.0], "min": [-0.010100529063493013, -0.0007703304290771484, -0.004846344469115138], "type": "VEC3"}, {"bufferView": 115, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 116, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.022139687789604068, 0.0007734298706054688, 0.012192064663395286], "min": [-0.02213968290016055, -0.0006520748138427734, -3.0472874641418457e-05], "type": "VEC3"}, {"bufferView": 117, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 118, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.001122710877098143, 0.0008530616760253906, 0.0031089731492102146], "min": [-0.001122712274082005, -0.001661539077758789, -0.0003454163670539856], "type": "VEC3"}, {"bufferView": 119, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 120, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.012424433836713433, 0.00883495807647705, 0.007022264413535595], "min": [-0.012424430809915066, 0.0, -0.0022277161478996277], "type": "VEC3"}, {"bufferView": 121, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 122, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.011312467977404594, 0.010434269905090332, 0.016753770411014557], "min": [-0.01131246518343687, -0.017862677574157715, -0.00177060067653656], "type": "VEC3"}, {"bufferView": 123, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 124, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.005696137435734272, 0.0023604631423950195, 0.006836690939962864], "min": [-0.005696136504411697, -0.01256406307220459, -0.00233333557844162], "type": "VEC3"}, {"bufferView": 125, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 126, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.008117489516735077, 0.009595036506652832, 0.017030246555805206], "min": [-0.008117482997477055, -0.01832294464111328, -0.003486134111881256], "type": "VEC3"}, {"bufferView": 127, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 128, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0014139795675873756, 1.1920928955078125e-07, 0.0003575459122657776], "min": [-0.0014139842242002487, -0.004284381866455078, -0.0007310360670089722], "type": "VEC3"}, {"bufferView": 129, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 130, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [1.329771475866437e-05, 1.1920928955078125e-07, 0.00035753846168518066], "min": [-0.0014139842242002487, -0.004284262657165527, -0.0007310360670089722], "type": "VEC3"}, {"bufferView": 131, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 132, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0014139795675873756, 1.1920928955078125e-07, 0.0003575459122657776], "min": [-2.987682819366455e-06, -0.004284381866455078, -0.0007310360670089722], "type": "VEC3"}, {"bufferView": 133, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 134, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.003578372299671173, 0.0071866512298583984, 0.007621202617883682], "min": [-0.0035783685743808746, -0.010481834411621094, -0.00277593731880188], "type": "VEC3"}, {"bufferView": 135, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 136, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.007976492634043097, 0.0017406940460205078, 0.005827129352837801], "min": [-0.007976488675922155, -0.0030362606048583984, -0.001231461763381958], "type": "VEC3"}, {"bufferView": 137, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 138, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.010625751223415136, 0.001822829246520996, 0.003585533704608679], "min": [-0.010625748429447412, -0.008756041526794434, -0.006524108350276947], "type": "VEC3"}, {"bufferView": 139, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 140, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.011383120203390718, 0.002997279167175293, 0.00892995111644268], "min": [-0.01138311717659235, -0.005559206008911133, -0.0005019232630729675], "type": "VEC3"}, {"bufferView": 141, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 142, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.010200009681284428, 0.007272481918334961, 0.017274343641474843], "min": [-0.0102000143378973, -0.015123724937438965, -0.000805780291557312], "type": "VEC3"}, {"bufferView": 143, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 144, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.01943912450224161, 0.023317456245422363, 0.06851199269294739], "min": [-0.019439107505604625, 0.0, 0.0], "type": "VEC3"}, {"bufferView": 145, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 146, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0017263321788050234, 0.004126429557800293, 0.0008871335157891735], "min": [-0.0017263315967284143, -0.005475759506225586, -0.0020099154444324085], "type": "VEC3"}, {"bufferView": 147, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 148, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0017263321788050234, 0.004126429557800293, 0.000577686121687293], "min": [-0.0017263315967284143, -0.00018906593322753906, -0.0020099154444324085], "type": "VEC3"}, {"bufferView": 149, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 150, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0010979195358231664, 0.00029671192169189453, 0.0008871335157891735], "min": [-0.0010979200014844537, -0.005475759506225586, -0.0016770483925938606], "type": "VEC3"}, {"bufferView": 151, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 152, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0025678056990727782, 0.005202531814575195, 0.0017114418296841905], "min": [-0.002567805931903422, -0.005460262298583984, -0.00450711720623076], "type": "VEC3"}, {"bufferView": 153, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 154, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.002286141214426607, 0.005202531814575195, 0.0013787157367914915], "min": [-0.0022861408651806414, -0.00016105175018310547, -0.00450711720623076], "type": "VEC3"}, {"bufferView": 155, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 156, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0025678056990727782, 0.00029671192169189453, 0.001711438104393892], "min": [-0.002567805931903422, -0.005460262298583984, -0.0016770483925938606], "type": "VEC3"}, {"bufferView": 157, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 158, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.001659020024817437, 0.005232334136962891, 0.0006714516202919185], "min": [-0.0016590202576480806, -0.004348039627075195, -0.0010896475287154317], "type": "VEC3"}, {"bufferView": 159, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 160, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.0016049263067543507, 0.0, 0.0006714516202919185], "min": [-0.001604926073923707, -0.004348039627075195, 0.0], "type": "VEC3"}, {"bufferView": 161, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 162, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [0.001659020024817437, 0.005232334136962891, 7.521361112594604e-05], "min": [-0.0016590202576480806, -1.3113021850585938e-06, -0.0010896475287154317], "type": "VEC3"}, {"bufferView": 163, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 164, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [6.640112906097784e-05, 0.0048906803131103516, 0.0016923223156481981], "min": [-6.64015192342049e-05, -0.004753589630126953, -0.0016162584797712043], "type": "VEC3"}, {"bufferView": 165, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 166, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [6.640112906097784e-05, 0.0048906803131103516, 0.0], "min": [-6.64015192342049e-05, 0.0, -0.0016162584797712043], "type": "VEC3"}, {"bufferView": 167, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 168, "byteOffset": 0, "componentType": 5126, "count": 3998, "max": [5.804962711408734e-05, 0.0, 0.0016923223156481981], "min": [-5.8046134654432535e-05, -0.004763364791870117, 0.0], "type": "VEC3"}, {"bufferView": 169, "byteOffset": 0, "componentType": 5126, "count": 3998, "type": "VEC3"}, {"bufferView": 170, "byteOffset": 0, "componentType": 5125, "count": 30066, "type": "SCALAR"}, {"bufferView": 171, "byteOffset": 0, "componentType": 5125, "count": 12774, "type": "SCALAR"}, {"bufferView": 172, "byteOffset": 0, "componentType": 5125, "count": 4182, "type": "SCALAR"}, {"bufferView": 173, "byteOffset": 0, "componentType": 5126, "count": 9473, "max": [0.6556846499443054, 1.4601482152938843, 0.14574286341667175], "min": [-0.6556845903396606, -2.541938215472328e-07, -0.13017471134662628], "type": "VEC3"}, {"bufferView": 174, "byteOffset": 0, "componentType": 5126, "count": 9473, "type": "VEC3"}, {"bufferView": 175, "byteOffset": 0, "componentType": 5126, "count": 9473, "type": "VEC2"}, {"bufferView": 176, "byteOffset": 0, "componentType": 5123, "count": 9473, "type": "VEC4"}, {"bufferView": 177, "byteOffset": 0, "componentType": 5126, "count": 9473, "type": "VEC4"}, {"bufferView": 178, "byteOffset": 0, "componentType": 5125, "count": 62244, "type": "SCALAR"}, {"bufferView": 179, "byteOffset": 0, "componentType": 5126, "count": 16492, "max": [0.22542279958724976, 1.496543049812317, 0.1547117829322815], "min": [-0.22542275488376617, 1.2286665439605713, -0.08605095744132996], "type": "VEC3"}, {"bufferView": 180, "byteOffset": 0, "componentType": 5126, "count": 16492, "type": "VEC3"}, {"bufferView": 181, "byteOffset": 0, "componentType": 5126, "count": 16492, "type": "VEC2"}, {"bufferView": 182, "byteOffset": 0, "componentType": 5123, "count": 16492, "type": "VEC4"}, {"bufferView": 183, "byteOffset": 0, "componentType": 5126, "count": 16492, "type": "VEC4"}], "asset": {"generator": "saturday06_blender_vrm_exporter_experimental_3.4.2", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 387137, "byteOffset": 0}, {"buffer": 0, "byteLength": 421691, "byteOffset": 387137}, {"buffer": 0, "byteLength": 1219247, "byteOffset": 808828}, {"buffer": 0, "byteLength": 1043371, "byteOffset": 2028075}, {"buffer": 0, "byteLength": 61168, "byteOffset": 3071446}, {"buffer": 0, "byteLength": 55054, "byteOffset": 3132614}, {"buffer": 0, "byteLength": 69, "byteOffset": 3187668}, {"buffer": 0, "byteLength": 6661, "byteOffset": 3187737}, {"buffer": 0, "byteLength": 69, "byteOffset": 3194398}, {"buffer": 0, "byteLength": 635736, "byteOffset": 3194467}, {"buffer": 0, "byteLength": 429792, "byteOffset": 3830203}, {"buffer": 0, "byteLength": 375252, "byteOffset": 4259995}, {"buffer": 0, "byteLength": 70540, "byteOffset": 4635247}, {"buffer": 0, "byteLength": 1818, "byteOffset": 4705787}, {"buffer": 0, "byteLength": 6976, "byteOffset": 4707608}, {"buffer": 0, "byteLength": 1344, "byteOffset": 4714584}, {"buffer": 0, "byteLength": 780, "byteOffset": 4715928}, {"buffer": 0, "byteLength": 780, "byteOffset": 4716708}, {"buffer": 0, "byteLength": 520, "byteOffset": 4717488}, {"buffer": 0, "byteLength": 1344, "byteOffset": 4718008}, {"buffer": 0, "byteLength": 780, "byteOffset": 4719352}, {"buffer": 0, "byteLength": 780, "byteOffset": 4720132}, {"buffer": 0, "byteLength": 520, "byteOffset": 4720912}, {"buffer": 0, "byteLength": 2496, "byteOffset": 4721432}, {"buffer": 0, "byteLength": 2592, "byteOffset": 4723928}, {"buffer": 0, "byteLength": 2592, "byteOffset": 4726520}, {"buffer": 0, "byteLength": 1728, "byteOffset": 4729112}, {"buffer": 0, "byteLength": 3288, "byteOffset": 4730840}, {"buffer": 0, "byteLength": 2784, "byteOffset": 4734128}, {"buffer": 0, "byteLength": 2784, "byteOffset": 4736912}, {"buffer": 0, "byteLength": 1856, "byteOffset": 4739696}, {"buffer": 0, "byteLength": 2496, "byteOffset": 4741552}, {"buffer": 0, "byteLength": 2592, "byteOffset": 4744048}, {"buffer": 0, "byteLength": 2592, "byteOffset": 4746640}, {"buffer": 0, "byteLength": 1728, "byteOffset": 4749232}, {"buffer": 0, "byteLength": 26088, "byteOffset": 4750960}, {"buffer": 0, "byteLength": 21456, "byteOffset": 4777048}, {"buffer": 0, "byteLength": 21456, "byteOffset": 4798504}, {"buffer": 0, "byteLength": 14304, "byteOffset": 4819960}, {"buffer": 0, "byteLength": 3456, "byteOffset": 4834264}, {"buffer": 0, "byteLength": 3000, "byteOffset": 4837720}, {"buffer": 0, "byteLength": 3000, "byteOffset": 4840720}, {"buffer": 0, "byteLength": 2000, "byteOffset": 4843720}, {"buffer": 0, "byteLength": 3456, "byteOffset": 4845720}, {"buffer": 0, "byteLength": 3000, "byteOffset": 4849176}, {"buffer": 0, "byteLength": 3000, "byteOffset": 4852176}, {"buffer": 0, "byteLength": 2000, "byteOffset": 4855176}, {"buffer": 0, "byteLength": 26688, "byteOffset": 4857176}, {"buffer": 0, "byteLength": 4584, "byteOffset": 4883864}, {"buffer": 0, "byteLength": 2136, "byteOffset": 4888448}, {"buffer": 0, "byteLength": 50784, "byteOffset": 4890584}, {"buffer": 0, "byteLength": 47976, "byteOffset": 4941368}, {"buffer": 0, "byteLength": 47976, "byteOffset": 4989344}, {"buffer": 0, "byteLength": 31984, "byteOffset": 5037320}, {"buffer": 0, "byteLength": 31984, "byteOffset": 5069304}, {"buffer": 0, "byteLength": 63968, "byteOffset": 5101288}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5165256}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5213232}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5261208}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5309184}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5357160}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5405136}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5453112}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5501088}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5549064}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5597040}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5645016}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5692992}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5740968}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5788944}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5836920}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5884896}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5932872}, {"buffer": 0, "byteLength": 47976, "byteOffset": 5980848}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6028824}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6076800}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6124776}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6172752}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6220728}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6268704}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6316680}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6364656}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6412632}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6460608}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6508584}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6556560}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6604536}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6652512}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6700488}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6748464}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6796440}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6844416}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6892392}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6940368}, {"buffer": 0, "byteLength": 47976, "byteOffset": 6988344}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7036320}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7084296}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7132272}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7180248}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7228224}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7276200}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7324176}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7372152}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7420128}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7468104}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7516080}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7564056}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7612032}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7660008}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7707984}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7755960}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7803936}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7851912}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7899888}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7947864}, {"buffer": 0, "byteLength": 47976, "byteOffset": 7995840}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8043816}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8091792}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8139768}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8187744}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8235720}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8283696}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8331672}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8379648}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8427624}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8475600}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8523576}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8571552}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8619528}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8667504}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8715480}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8763456}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8811432}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8859408}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8907384}, {"buffer": 0, "byteLength": 47976, "byteOffset": 8955360}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9003336}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9051312}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9099288}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9147264}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9195240}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9243216}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9291192}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9339168}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9387144}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9435120}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9483096}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9531072}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9579048}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9627024}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9675000}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9722976}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9770952}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9818928}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9866904}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9914880}, {"buffer": 0, "byteLength": 47976, "byteOffset": 9962856}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10010832}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10058808}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10106784}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10154760}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10202736}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10250712}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10298688}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10346664}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10394640}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10442616}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10490592}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10538568}, {"buffer": 0, "byteLength": 47976, "byteOffset": 10586544}, {"buffer": 0, "byteLength": 120264, "byteOffset": 10634520}, {"buffer": 0, "byteLength": 51096, "byteOffset": 10754784}, {"buffer": 0, "byteLength": 16728, "byteOffset": 10805880}, {"buffer": 0, "byteLength": 113676, "byteOffset": 10822608}, {"buffer": 0, "byteLength": 113676, "byteOffset": 10936284}, {"buffer": 0, "byteLength": 75784, "byteOffset": 11049960}, {"buffer": 0, "byteLength": 75784, "byteOffset": 11125744}, {"buffer": 0, "byteLength": 151568, "byteOffset": 11201528}, {"buffer": 0, "byteLength": 248976, "byteOffset": 11353096}, {"buffer": 0, "byteLength": 197904, "byteOffset": 11602072}, {"buffer": 0, "byteLength": 197904, "byteOffset": 11799976}, {"buffer": 0, "byteLength": 131936, "byteOffset": 11997880}, {"buffer": 0, "byteLength": 131936, "byteOffset": 12129816}, {"buffer": 0, "byteLength": 263872, "byteOffset": 12261752}, {"buffer": 0, "byteLength": 1478209, "byteOffset": 12525624}], "buffers": [{"byteLength": 14003833}], "extensions": {"VRM": {"blendShapeMaster": {"blendShapeGroups": [{"binds": [{"index": 0, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Neutral", "presetName": "neutral"}, {"binds": [{"index": 39, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "A", "presetName": "a"}, {"binds": [{"index": 40, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "I", "presetName": "i"}, {"binds": [{"index": 41, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "U", "presetName": "u"}, {"binds": [{"index": 42, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "E", "presetName": "e"}, {"binds": [{"index": 43, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "O", "presetName": "o"}, {"binds": [{"index": 13, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Blink", "presetName": "blink"}, {"binds": [{"index": 15, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Blink_L", "presetName": "blink_l"}, {"binds": [{"index": 14, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Blink_R", "presetName": "blink_r"}, {"binds": [{"index": 1, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Angry", "presetName": "angry"}, {"binds": [{"index": 2, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Fun", "presetName": "fun"}, {"binds": [{"index": 3, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "<PERSON>", "presetName": "joy"}, {"binds": [{"index": 4, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Sorrow", "presetName": "sorrow"}, {"binds": [{"index": 5, "mesh": 8, "weight": 100.0}], "isBinary": false, "materialValues": [], "name": "Surprised", "presetName": "unknown"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookUp", "presetName": "lookup"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookDown", "presetName": "lookdown"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookLeft", "presetName": "lookleft"}, {"binds": [], "isBinary": false, "materialValues": [], "name": "LookRight", "presetName": "lookright"}]}, "exporterVersion": "saturday06_blender_vrm_exporter_experimental_3.4.2", "firstPerson": {"firstPersonBone": 10, "firstPersonBoneOffset": {"x": 0.0, "y": 0.05999999865889549, "z": 0.0}, "lookAtHorizontalInner": {"curve": [0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0], "xRange": 90.0, "yRange": 8.0}, "lookAtHorizontalOuter": {"curve": [0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0], "xRange": 90.0, "yRange": 12.0}, "lookAtTypeName": "Bone", "lookAtVerticalDown": {"curve": [0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0], "xRange": 90.0, "yRange": 10.0}, "lookAtVerticalUp": {"curve": [0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0], "xRange": 90.0, "yRange": 10.0}, "meshAnnotations": [{"firstPersonFlag": "Auto", "mesh": 8}, {"firstPersonFlag": "Auto", "mesh": 9}, {"firstPersonFlag": "Auto", "mesh": 10}]}, "humanoid": {"armStretch": 0.05000000074505806, "feetSpacing": 0.0, "hasTranslationDoF": false, "humanBones": [{"bone": "hips", "node": 1, "useDefaultValues": true}, {"bone": "spine", "node": 2, "useDefaultValues": true}, {"bone": "chest", "node": 3, "useDefaultValues": true}, {"bone": "upperChest", "node": 4, "useDefaultValues": true}, {"bone": "neck", "node": 9, "useDefaultValues": true}, {"bone": "head", "node": 10, "useDefaultValues": true}, {"bone": "leftEye", "node": 11, "useDefaultValues": true}, {"bone": "rightEye", "node": 12, "useDefaultValues": true}, {"bone": "leftUpperLeg", "node": 87, "useDefaultValues": true}, {"bone": "leftLowerLeg", "node": 94, "useDefaultValues": true}, {"bone": "leftFoot", "node": 95, "useDefaultValues": true}, {"bone": "leftToes", "node": 96, "useDefaultValues": true}, {"bone": "rightUpperLeg", "node": 98, "useDefaultValues": true}, {"bone": "rightLowerLeg", "node": 105, "useDefaultValues": true}, {"bone": "rightFoot", "node": 106, "useDefaultValues": true}, {"bone": "rightToes", "node": 107, "useDefaultValues": true}, {"bone": "leftShoulder", "node": 45, "useDefaultValues": true}, {"bone": "leftUpperArm", "node": 46, "useDefaultValues": true}, {"bone": "leftLowerArm", "node": 47, "useDefaultValues": true}, {"bone": "leftHand", "node": 48, "useDefaultValues": true}, {"bone": "rightShoulder", "node": 65, "useDefaultValues": true}, {"bone": "rightUpperArm", "node": 66, "useDefaultValues": true}, {"bone": "rightLowerArm", "node": 67, "useDefaultValues": true}, {"bone": "rightHand", "node": 68, "useDefaultValues": true}, {"bone": "leftThumbProximal", "node": 61, "useDefaultValues": true}, {"bone": "leftThumbIntermediate", "node": 62, "useDefaultValues": true}, {"bone": "leftThumbDistal", "node": 63, "useDefaultValues": true}, {"bone": "leftIndexProximal", "node": 49, "useDefaultValues": true}, {"bone": "leftIndexIntermediate", "node": 50, "useDefaultValues": true}, {"bone": "leftIndexDistal", "node": 51, "useDefaultValues": true}, {"bone": "leftMiddleProximal", "node": 55, "useDefaultValues": true}, {"bone": "leftMiddleIntermediate", "node": 56, "useDefaultValues": true}, {"bone": "leftMiddleDistal", "node": 57, "useDefaultValues": true}, {"bone": "leftRingProximal", "node": 58, "useDefaultValues": true}, {"bone": "leftRingIntermediate", "node": 59, "useDefaultValues": true}, {"bone": "leftRingDistal", "node": 60, "useDefaultValues": true}, {"bone": "leftLittleProximal", "node": 52, "useDefaultValues": true}, {"bone": "leftLittleIntermediate", "node": 53, "useDefaultValues": true}, {"bone": "leftLittleDistal", "node": 54, "useDefaultValues": true}, {"bone": "rightThumbProximal", "node": 82, "useDefaultValues": true}, {"bone": "rightThumbIntermediate", "node": 83, "useDefaultValues": true}, {"bone": "rightThumbDistal", "node": 84, "useDefaultValues": true}, {"bone": "rightIndexProximal", "node": 69, "useDefaultValues": true}, {"bone": "rightIndexIntermediate", "node": 70, "useDefaultValues": true}, {"bone": "rightIndexDistal", "node": 71, "useDefaultValues": true}, {"bone": "rightMiddleProximal", "node": 75, "useDefaultValues": true}, {"bone": "rightMiddleIntermediate", "node": 76, "useDefaultValues": true}, {"bone": "rightMiddleDistal", "node": 77, "useDefaultValues": true}, {"bone": "rightRingProximal", "node": 79, "useDefaultValues": true}, {"bone": "rightRingIntermediate", "node": 80, "useDefaultValues": true}, {"bone": "rightRingDistal", "node": 81, "useDefaultValues": true}, {"bone": "rightLittleProximal", "node": 72, "useDefaultValues": true}, {"bone": "rightLittleIntermediate", "node": 73, "useDefaultValues": true}, {"bone": "rightLittleDistal", "node": 74, "useDefaultValues": true}], "legStretch": 0.05000000074505806, "lowerArmTwist": 0.5, "lowerLegTwist": 0.5, "upperArmTwist": 0.5, "upperLegTwist": 0.5}, "materialProperties": [{"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 1.0, "_RimLift": 0.0, "_RimLightingMix": 0.0, "_ShadeShift": -1.1175870895385742e-08, "_ShadeToony": 0.8999999772757294, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true}, "name": "lambert6 (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_MainTex": 0}, "vectorProperties": {"_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [0.0, 0.0, 0.0, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [0.0, 0.0, 0.0, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 1.0, "_RimLift": 0.0, "_RimLightingMix": 0.0, "_ShadeShift": -1.1175870895385742e-08, "_ShadeToony": 0.8999999772757294, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true}, "name": "lambert3 (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_MainTex": 1}, "vectorProperties": {"_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [0.0, 0.0, 0.0, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [0.0, 0.0, 0.0, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_FaceMouth_00_FACE (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 2, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 10, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 5, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 0}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": true, "_ALPHAPREMULTIPLY_ON": false, "_NORMALMAP": true}, "name": "N00_000_00_EyeIris_00_EYE (Instance)", "renderQueue": 3000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Transparent"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 2, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 10, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.0, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 5, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 0}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "_ALPHABLEND_ON": true, "_ALPHAPREMULTIPLY_ON": false, "_NORMALMAP": true}, "name": "N00_000_00_EyeHighlight_00_EYE (Instance)", "renderQueue": 3500, "shader": "VRM/MToon", "tagMap": {"RenderType": "Transparent"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.0, 0.0, 0.0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.0, 0.0, 0.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.6416666507720947, "_ShadeToony": 0.9083333321230868, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_Face_00_SKIN (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 4, "_EmissionMap": 5, "_MainTex": 2, "_OutlineWidthTexture": 7, "_ShadeTexture": 3, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.2833333313243593, 0.2833333313243593, 0.2833333313243593, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 16.84203338623047, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.2500009834766388, "_ShadeToony": 0.3750000149011495, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_Body_00_SKIN (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [1.0, 1.0, 1.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 16.84203338623047, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": -0.2500009834766388, "_ShadeToony": 0.3750000149011495, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_006_01_Shoes_01_CLOTH (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.2745097598367326, 0.09019601881179808, 0.12549012864217834, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [1.0, 1.0, 1.0, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 2, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": 1.4901161193847656e-08, "_ShadeToony": 0.6000000178813937, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_00_HairBack_00_HAIR (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.474509774457842, 0.7450980438916915, 0.7372549929921988, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.24999999896718345, 0.24999999896718345, 0.24999999896718345, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1.0, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.10000002384185791, "_LightColorAttenuation": 0.0, "_MToonVersion": 32, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 0.0, "_OutlineScaledMaxDistance": 1.0, "_OutlineWidth": 0.24999999441206455, "_OutlineWidthMode": 1, "_ReceiveShadowRate": 1.0, "_RimFresnelPower": 100.0, "_RimLift": 0.10000000149011612, "_RimLightingMix": 0.0, "_ShadeShift": 1.4901161193847656e-08, "_ShadeToony": 0.6000000178813937, "_ShadingGradeRate": 1.0, "_SrcBlend": 1, "_UvAnimRotation": 0.0, "_UvAnimScrollX": 0.0, "_UvAnimScrollY": 0.0, "_ZWrite": 1}, "keywordMap": {"MTOON_DEBUG_LITSHADERATE": false, "MTOON_DEBUG_NORMAL": false, "MTOON_OUTLINE_COLOR_FIXED": true, "MTOON_OUTLINE_WIDTH_WORLD": true, "_ALPHABLEND_ON": false, "_ALPHAPREMULTIPLY_ON": false, "_ALPHATEST_ON": true, "_NORMALMAP": true}, "name": "N00_000_Hair_00_HAIR_01 (Instance)", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_BumpMap": 11, "_EmissionMap": 12, "_MainTex": 9, "_OutlineWidthTexture": 13, "_ShadeTexture": 10, "_SphereAdd": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1.0, 1.0, 1.0, 1.0], "_EmissionColor": [1.0, 1.0, 1.0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0.0, 0.0, 1.0, 1.0], "_OutlineColor": [0.27450970391685836, 0.09019597704651616, 0.12549006733273893, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_RimColor": [0.24999999896718345, 0.24999999896718345, 0.24999999896718345, 1], "_ShadeColor": [1.0, 1.0, 1.0, 1], "_ShadeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1]}}], "meta": {"allowedUserName": "Everyone", "author": "曹仁", "commercialUssageName": "Disallow", "contactInformation": "", "licenseName": "Redistribution_Prohibited", "otherLicenseUrl": "", "otherPermissionUrl": "", "reference": "", "sexualUssageName": "Disallow", "texture": 14, "title": "豪德寺美弥子 Bubbles", "version": "", "violentUssageName": "Disallow"}, "secondaryAnimation": {"boneGroups": [{"bones": [5, 7], "center": 0, "colliderGroups": [], "comment": "Bust", "dragForce": 0.05000000074505806, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.00016627233708277345, "stiffiness": 0.75}, {"bones": [88, 90, 92, 99, 101, 103], "center": 0, "colliderGroups": [10, 11], "comment": "Skirt", "dragForce": 0.05000000074505806, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.010646205395460129, "stiffiness": 0.5}, {"bones": [13], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.0074600474908947945, "stiffiness": 0.8500000238418579}, {"bones": [22], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.007460050750523806, "stiffiness": 0.8500000238418579}, {"bones": [31], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.0076298341155052185, "stiffiness": 0.4000000059604645}, {"bones": [34], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.007539203856140375, "stiffiness": 0.7250000238418579}, {"bones": [37], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.0075392029248178005, "stiffiness": 0.7250000238418579}, {"bones": [40], "center": 0, "colliderGroups": [3, 4, 7, 5, 8, 6, 9, 1, 0, 2], "comment": "Hair", "dragForce": 0.4000000059604645, "gravityDir": {"x": 0.0, "y": -1.0, "z": 0.0}, "gravityPower": 0.0, "hitRadius": 0.007629828527569771, "stiffiness": 0.4000000059604645}], "colliderGroups": [{"colliders": [{"offset": {"x": 4.440892098500626e-16, "y": 0.0, "z": -0.0}, "radius": 0.10922932624816895}], "node": 2}, {"colliders": [{"offset": {"x": -2.220446049250313e-15, "y": -2.384185791015625e-07, "z": 0.009102512151002884}, "radius": 0.09102444350719452}, {"offset": {"x": -0.045512217232177576, "y": 0.05916595458984375, "z": -0.009102409705519676}, "radius": 0.06371711194515228}, {"offset": {"x": 0.045512222549726644, "y": 0.05916595458984375, "z": -0.009102409705519676}, "radius": 0.06371711194515228}], "node": 4}, {"colliders": [{"offset": {"x": -3.552713678800501e-15, "y": 0.02730739116668701, "z": 0.01183316484093666}, "radius": 0.04551222175359726}], "node": 9}, {"colliders": [{"offset": {"x": -0.0, "y": 0.09614157676696777, "z": -0.013844376429915428}, "radius": 0.0961415097117424}], "node": 10}, {"colliders": [{"offset": {"x": 5.21540641784668e-08, "y": -0.009102463722229004, "z": -0.0}, "radius": 0.04551222728738091}, {"offset": {"x": -0.0682682916522026, "y": -0.009102463722229004, "z": 1.862645149230957e-09}, "radius": 0.04551222728738091}, {"offset": {"x": -0.1365366354584694, "y": -0.009102463722229004, "z": -1.862645149230957e-09}, "radius": 0.04551222728738091}], "node": 46}, {"colliders": [{"offset": {"x": -5.960464477539063e-08, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.02730733637242855}, {"offset": {"x": -0.045512259006500244, "y": 0.0, "z": -0.0}, "radius": 0.031858560963811865}, {"offset": {"x": -0.09102451801300049, "y": 2.384185791015625e-07, "z": 1.862645149230957e-09}, "radius": 0.02730733637242855}, {"offset": {"x": -0.13653677701950073, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.02730733637242855}], "node": 47}, {"colliders": [{"offset": {"x": -0.015798509120941162, "y": 0.0, "z": -1.862645149230957e-08}, "radius": 0.02369768870477499}], "node": 48}, {"colliders": [{"offset": {"x": -1.4901161193847656e-08, "y": -0.009102344512939453, "z": 1.862645149230957e-09}, "radius": 0.04551222547888756}, {"offset": {"x": 0.06826832890510559, "y": -0.009102344512939453, "z": 1.862645149230957e-09}, "radius": 0.04551222547888756}, {"offset": {"x": 0.13653667271137238, "y": -0.009102344512939453, "z": 3.725290298461914e-09}, "radius": 0.04551222547888756}], "node": 66}, {"colliders": [{"offset": {"x": 2.9802322387695312e-08, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.027307335287332535}, {"offset": {"x": 0.045512259006500244, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.03185855969786644}, {"offset": {"x": 0.09102451801300049, "y": 2.384185791015625e-07, "z": 1.862645149230957e-09}, "radius": 0.027307335287332535}, {"offset": {"x": 0.13653677701950073, "y": 0.0, "z": 1.862645149230957e-09}, "radius": 0.027307335287332535}], "node": 67}, {"colliders": [{"offset": {"x": 0.015798628330230713, "y": -1.1920928955078125e-07, "z": -1.4901161193847656e-08}, "radius": 0.02369768684212988}], "node": 68}, {"colliders": [{"offset": {"x": 7.450580596923828e-09, "y": 5.960464477539063e-08, "z": 4.656612873077393e-10}, "radius": 0.07372977743706835}, {"offset": {"x": -0.0, "y": -0.10922932624816895, "z": -9.313225746154785e-10}, "radius": 0.07372977743706835}, {"offset": {"x": 7.450580596923828e-09, "y": -0.20025372505187988, "z": -2.3283064365386963e-10}, "radius": 0.07372977743706835}], "node": 87}, {"colliders": [{"offset": {"x": 7.450580596923828e-09, "y": 5.960464477539063e-08, "z": -1.3969838619232178e-09}, "radius": 0.07372977743706835}, {"offset": {"x": -0.0, "y": -0.10922932624816895, "z": -4.656612873077393e-10}, "radius": 0.07372977743706835}, {"offset": {"x": -0.0, "y": -0.20025372505187988, "z": -0.0}, "radius": 0.07372977743706835}], "node": 98}]}, "specVersion": "0.0"}}, "extensionsUsed": ["KHR_materials_unlit", "KHR_texture_transform", "VRM"], "images": [{"bufferView": 0, "mimeType": "image/png", "name": "file5"}, {"bufferView": 1, "mimeType": "image/png", "name": "file2"}, {"bufferView": 2, "mimeType": "image/png", "name": "texture_0"}, {"bufferView": 3, "mimeType": "image/png", "name": "texture_7"}, {"bufferView": 4, "mimeType": "image/png", "name": "texture_2"}, {"bufferView": 5, "mimeType": "image/png", "name": "texture_1"}, {"bufferView": 6, "mimeType": "image/png", "name": "Shader_NoneBlack"}, {"bufferView": 7, "mimeType": "image/png", "name": "texture_9"}, {"bufferView": 8, "mimeType": "image/png", "name": "Mat<PERSON><PERSON>ar<PERSON>"}, {"bufferView": 9, "mimeType": "image/png", "name": "texture_3"}, {"bufferView": 10, "mimeType": "image/png", "name": "texture_11"}, {"bufferView": 11, "mimeType": "image/png", "name": "texture_5"}, {"bufferView": 12, "mimeType": "image/png", "name": "texture_4"}, {"bufferView": 13, "mimeType": "image/png", "name": "texture_12"}, {"bufferView": 184, "mimeType": "image/png", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "materials": [{"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": false, "extensions": {"KHR_materials_unlit": {}}, "name": "lambert6 (Instance)", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": false, "extensions": {"KHR_materials_unlit": {}}, "name": "lambert3 (Instance)", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 1}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": false, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 5}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_00_FaceMouth_00_FACE (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 4, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 2}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaMode": "BLEND", "doubleSided": false, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 5}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_00_EyeIris_00_EYE (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 4, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 2}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaMode": "BLEND", "doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 5}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_00_EyeHighlight_00_EYE (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 4, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 2}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 5}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_00_Face_00_SKIN (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 4, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 2}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": false, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 12}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_00_Body_00_SKIN (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 11, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 9}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 12}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_006_01_Shoes_01_CLOTH (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 11, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 9}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": false, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 12}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_00_HairBack_00_HAIR (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 11, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 9}, "metallicFactor": 0, "roughnessFactor": 0.9}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 12}, "extensions": {"KHR_materials_unlit": {}}, "name": "N00_000_Hair_00_HAIR_01 (Instance)", "normalTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 11, "scale": 1.0}, "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 1.0], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0.0, 0.0], "scale": [1.0, 1.0]}}, "index": 9}, "metallicFactor": 0, "roughnessFactor": 0.9}}], "meshes": [{"name": "pSphere1", "primitives": [{"attributes": {"NORMAL": 3, "POSITION": 2, "TEXCOORD_0": 4}, "indices": 1, "material": 0}]}, {"name": "pSphere1.001", "primitives": [{"attributes": {"NORMAL": 7, "POSITION": 6, "TEXCOORD_0": 8}, "indices": 5, "material": 0}]}, {"name": "polySurface38", "primitives": [{"attributes": {"NORMAL": 11, "POSITION": 10, "TEXCOORD_0": 12}, "indices": 9, "material": 0}]}, {"name": "polySurface36", "primitives": [{"attributes": {"NORMAL": 15, "POSITION": 14, "TEXCOORD_0": 16}, "indices": 13, "material": 0}]}, {"name": "polySurface40", "primitives": [{"attributes": {"NORMAL": 19, "POSITION": 18, "TEXCOORD_0": 20}, "indices": 17, "material": 0}]}, {"name": "polySurface33", "primitives": [{"attributes": {"NORMAL": 23, "POSITION": 22, "TEXCOORD_0": 24}, "indices": 21, "material": 1}]}, {"name": "polySurface39", "primitives": [{"attributes": {"NORMAL": 27, "POSITION": 26, "TEXCOORD_0": 28}, "indices": 25, "material": 0}]}, {"name": "polySurface35", "primitives": [{"attributes": {"NORMAL": 31, "POSITION": 30, "TEXCOORD_0": 32}, "indices": 29, "material": 0}]}, {"name": "Face (merged).baked", "primitives": [{"attributes": {"JOINTS_0": 40, "NORMAL": 38, "POSITION": 37, "TEXCOORD_0": 39, "WEIGHTS_0": 41}, "extras": {"targetNames": ["Fcl_ALL_Neutral", "Fcl_ALL_Angry", "Fcl_ALL_Fun", "Fcl_ALL_Joy", "Fcl_ALL_Sorrow", "Fcl_ALL_Surprised", "Fcl_BRW_Angry", "Fcl_BRW_Fun", "Fcl_BRW_Joy", "Fcl_BRW_Sorrow", "Fcl_BRW_Surprised", "Fcl_EYE_Natural", "Fcl_EYE_Angry", "Fcl_EYE_Close", "Fcl_EYE_Close_R", "Fcl_EYE_Close_L", "Fcl_EYE_Fun", "Fcl_EYE_Joy", "Fcl_EYE_Joy_R", "Fcl_EYE_Joy_L", "Fcl_EYE_Sorrow", "Fcl_EYE_Surprised", "Fcl_EYE_Spread", "Fcl_EY<PERSON>_Iris_Hide", "Fcl_EYE_Highlight_Hide", "Fcl_MTH_Close", "Fcl_MTH_Up", "Fcl_MTH_Down", "Fcl_MTH_Angry", "Fcl_MTH_Small", "Fcl_MTH_Large", "Fcl_MTH_Neutral", "Fcl_MTH_Fun", "Fcl_MTH_Joy", "Fcl_MTH_Sorrow", "Fcl_MTH_Surprised", "Fcl_MTH_SkinFung", "Fcl_MTH_SkinFung_R", "Fcl_MTH_SkinFung_L", "Fcl_MTH_A", "Fcl_MTH_I", "Fcl_MTH_U", "Fcl_MTH_E", "Fcl_MTH_O", "Fcl_HA_Hide", "Fcl_HA_Fung1", "Fcl_HA_Fung1_Low", "Fcl_HA_Fung1_Up", "Fcl_HA_Fung2", "Fcl_HA_Fung2_Low", "Fcl_HA_Fung2_Up", "Fcl_HA_Fung3", "Fcl_HA_Fung3_Up", "Fcl_HA_Fung3_Low", "Fcl_HA_Short", "Fcl_HA_Short_Up", "Fcl_HA_Short_Low"]}, "indices": 33, "material": 2, "targets": [{"NORMAL": 43, "POSITION": 42}, {"NORMAL": 45, "POSITION": 44}, {"NORMAL": 47, "POSITION": 46}, {"NORMAL": 49, "POSITION": 48}, {"NORMAL": 51, "POSITION": 50}, {"NORMAL": 53, "POSITION": 52}, {"NORMAL": 55, "POSITION": 54}, {"NORMAL": 57, "POSITION": 56}, {"NORMAL": 59, "POSITION": 58}, {"NORMAL": 61, "POSITION": 60}, {"NORMAL": 63, "POSITION": 62}, {"NORMAL": 65, "POSITION": 64}, {"NORMAL": 67, "POSITION": 66}, {"NORMAL": 69, "POSITION": 68}, {"NORMAL": 71, "POSITION": 70}, {"NORMAL": 73, "POSITION": 72}, {"NORMAL": 75, "POSITION": 74}, {"NORMAL": 77, "POSITION": 76}, {"NORMAL": 79, "POSITION": 78}, {"NORMAL": 81, "POSITION": 80}, {"NORMAL": 83, "POSITION": 82}, {"NORMAL": 85, "POSITION": 84}, {"NORMAL": 87, "POSITION": 86}, {"NORMAL": 89, "POSITION": 88}, {"NORMAL": 91, "POSITION": 90}, {"NORMAL": 93, "POSITION": 92}, {"NORMAL": 95, "POSITION": 94}, {"NORMAL": 97, "POSITION": 96}, {"NORMAL": 99, "POSITION": 98}, {"NORMAL": 101, "POSITION": 100}, {"NORMAL": 103, "POSITION": 102}, {"NORMAL": 105, "POSITION": 104}, {"NORMAL": 107, "POSITION": 106}, {"NORMAL": 109, "POSITION": 108}, {"NORMAL": 111, "POSITION": 110}, {"NORMAL": 113, "POSITION": 112}, {"NORMAL": 115, "POSITION": 114}, {"NORMAL": 117, "POSITION": 116}, {"NORMAL": 119, "POSITION": 118}, {"NORMAL": 121, "POSITION": 120}, {"NORMAL": 123, "POSITION": 122}, {"NORMAL": 125, "POSITION": 124}, {"NORMAL": 127, "POSITION": 126}, {"NORMAL": 129, "POSITION": 128}, {"NORMAL": 131, "POSITION": 130}, {"NORMAL": 133, "POSITION": 132}, {"NORMAL": 135, "POSITION": 134}, {"NORMAL": 137, "POSITION": 136}, {"NORMAL": 139, "POSITION": 138}, {"NORMAL": 141, "POSITION": 140}, {"NORMAL": 143, "POSITION": 142}, {"NORMAL": 145, "POSITION": 144}, {"NORMAL": 147, "POSITION": 146}, {"NORMAL": 149, "POSITION": 148}, {"NORMAL": 151, "POSITION": 150}, {"NORMAL": 153, "POSITION": 152}, {"NORMAL": 155, "POSITION": 154}]}, {"attributes": {"JOINTS_0": 40, "NORMAL": 38, "POSITION": 37, "TEXCOORD_0": 39, "WEIGHTS_0": 41}, "extras": {"targetNames": ["Fcl_ALL_Neutral", "Fcl_ALL_Angry", "Fcl_ALL_Fun", "Fcl_ALL_Joy", "Fcl_ALL_Sorrow", "Fcl_ALL_Surprised", "Fcl_BRW_Angry", "Fcl_BRW_Fun", "Fcl_BRW_Joy", "Fcl_BRW_Sorrow", "Fcl_BRW_Surprised", "Fcl_EYE_Natural", "Fcl_EYE_Angry", "Fcl_EYE_Close", "Fcl_EYE_Close_R", "Fcl_EYE_Close_L", "Fcl_EYE_Fun", "Fcl_EYE_Joy", "Fcl_EYE_Joy_R", "Fcl_EYE_Joy_L", "Fcl_EYE_Sorrow", "Fcl_EYE_Surprised", "Fcl_EYE_Spread", "Fcl_EY<PERSON>_Iris_Hide", "Fcl_EYE_Highlight_Hide", "Fcl_MTH_Close", "Fcl_MTH_Up", "Fcl_MTH_Down", "Fcl_MTH_Angry", "Fcl_MTH_Small", "Fcl_MTH_Large", "Fcl_MTH_Neutral", "Fcl_MTH_Fun", "Fcl_MTH_Joy", "Fcl_MTH_Sorrow", "Fcl_MTH_Surprised", "Fcl_MTH_SkinFung", "Fcl_MTH_SkinFung_R", "Fcl_MTH_SkinFung_L", "Fcl_MTH_A", "Fcl_MTH_I", "Fcl_MTH_U", "Fcl_MTH_E", "Fcl_MTH_O", "Fcl_HA_Hide", "Fcl_HA_Fung1", "Fcl_HA_Fung1_Low", "Fcl_HA_Fung1_Up", "Fcl_HA_Fung2", "Fcl_HA_Fung2_Low", "Fcl_HA_Fung2_Up", "Fcl_HA_Fung3", "Fcl_HA_Fung3_Up", "Fcl_HA_Fung3_Low", "Fcl_HA_Short", "Fcl_HA_Short_Up", "Fcl_HA_Short_Low"]}, "indices": 34, "material": 3, "targets": [{"NORMAL": 43, "POSITION": 42}, {"NORMAL": 45, "POSITION": 44}, {"NORMAL": 47, "POSITION": 46}, {"NORMAL": 49, "POSITION": 48}, {"NORMAL": 51, "POSITION": 50}, {"NORMAL": 53, "POSITION": 52}, {"NORMAL": 55, "POSITION": 54}, {"NORMAL": 57, "POSITION": 56}, {"NORMAL": 59, "POSITION": 58}, {"NORMAL": 61, "POSITION": 60}, {"NORMAL": 63, "POSITION": 62}, {"NORMAL": 65, "POSITION": 64}, {"NORMAL": 67, "POSITION": 66}, {"NORMAL": 69, "POSITION": 68}, {"NORMAL": 71, "POSITION": 70}, {"NORMAL": 73, "POSITION": 72}, {"NORMAL": 75, "POSITION": 74}, {"NORMAL": 77, "POSITION": 76}, {"NORMAL": 79, "POSITION": 78}, {"NORMAL": 81, "POSITION": 80}, {"NORMAL": 83, "POSITION": 82}, {"NORMAL": 85, "POSITION": 84}, {"NORMAL": 87, "POSITION": 86}, {"NORMAL": 89, "POSITION": 88}, {"NORMAL": 91, "POSITION": 90}, {"NORMAL": 93, "POSITION": 92}, {"NORMAL": 95, "POSITION": 94}, {"NORMAL": 97, "POSITION": 96}, {"NORMAL": 99, "POSITION": 98}, {"NORMAL": 101, "POSITION": 100}, {"NORMAL": 103, "POSITION": 102}, {"NORMAL": 105, "POSITION": 104}, {"NORMAL": 107, "POSITION": 106}, {"NORMAL": 109, "POSITION": 108}, {"NORMAL": 111, "POSITION": 110}, {"NORMAL": 113, "POSITION": 112}, {"NORMAL": 115, "POSITION": 114}, {"NORMAL": 117, "POSITION": 116}, {"NORMAL": 119, "POSITION": 118}, {"NORMAL": 121, "POSITION": 120}, {"NORMAL": 123, "POSITION": 122}, {"NORMAL": 125, "POSITION": 124}, {"NORMAL": 127, "POSITION": 126}, {"NORMAL": 129, "POSITION": 128}, {"NORMAL": 131, "POSITION": 130}, {"NORMAL": 133, "POSITION": 132}, {"NORMAL": 135, "POSITION": 134}, {"NORMAL": 137, "POSITION": 136}, {"NORMAL": 139, "POSITION": 138}, {"NORMAL": 141, "POSITION": 140}, {"NORMAL": 143, "POSITION": 142}, {"NORMAL": 145, "POSITION": 144}, {"NORMAL": 147, "POSITION": 146}, {"NORMAL": 149, "POSITION": 148}, {"NORMAL": 151, "POSITION": 150}, {"NORMAL": 153, "POSITION": 152}, {"NORMAL": 155, "POSITION": 154}]}, {"attributes": {"JOINTS_0": 40, "NORMAL": 38, "POSITION": 37, "TEXCOORD_0": 39, "WEIGHTS_0": 41}, "extras": {"targetNames": ["Fcl_ALL_Neutral", "Fcl_ALL_Angry", "Fcl_ALL_Fun", "Fcl_ALL_Joy", "Fcl_ALL_Sorrow", "Fcl_ALL_Surprised", "Fcl_BRW_Angry", "Fcl_BRW_Fun", "Fcl_BRW_Joy", "Fcl_BRW_Sorrow", "Fcl_BRW_Surprised", "Fcl_EYE_Natural", "Fcl_EYE_Angry", "Fcl_EYE_Close", "Fcl_EYE_Close_R", "Fcl_EYE_Close_L", "Fcl_EYE_Fun", "Fcl_EYE_Joy", "Fcl_EYE_Joy_R", "Fcl_EYE_Joy_L", "Fcl_EYE_Sorrow", "Fcl_EYE_Surprised", "Fcl_EYE_Spread", "Fcl_EY<PERSON>_Iris_Hide", "Fcl_EYE_Highlight_Hide", "Fcl_MTH_Close", "Fcl_MTH_Up", "Fcl_MTH_Down", "Fcl_MTH_Angry", "Fcl_MTH_Small", "Fcl_MTH_Large", "Fcl_MTH_Neutral", "Fcl_MTH_Fun", "Fcl_MTH_Joy", "Fcl_MTH_Sorrow", "Fcl_MTH_Surprised", "Fcl_MTH_SkinFung", "Fcl_MTH_SkinFung_R", "Fcl_MTH_SkinFung_L", "Fcl_MTH_A", "Fcl_MTH_I", "Fcl_MTH_U", "Fcl_MTH_E", "Fcl_MTH_O", "Fcl_HA_Hide", "Fcl_HA_Fung1", "Fcl_HA_Fung1_Low", "Fcl_HA_Fung1_Up", "Fcl_HA_Fung2", "Fcl_HA_Fung2_Low", "Fcl_HA_Fung2_Up", "Fcl_HA_Fung3", "Fcl_HA_Fung3_Up", "Fcl_HA_Fung3_Low", "Fcl_HA_Short", "Fcl_HA_Short_Up", "Fcl_HA_Short_Low"]}, "indices": 35, "material": 4, "targets": [{"NORMAL": 43, "POSITION": 42}, {"NORMAL": 45, "POSITION": 44}, {"NORMAL": 47, "POSITION": 46}, {"NORMAL": 49, "POSITION": 48}, {"NORMAL": 51, "POSITION": 50}, {"NORMAL": 53, "POSITION": 52}, {"NORMAL": 55, "POSITION": 54}, {"NORMAL": 57, "POSITION": 56}, {"NORMAL": 59, "POSITION": 58}, {"NORMAL": 61, "POSITION": 60}, {"NORMAL": 63, "POSITION": 62}, {"NORMAL": 65, "POSITION": 64}, {"NORMAL": 67, "POSITION": 66}, {"NORMAL": 69, "POSITION": 68}, {"NORMAL": 71, "POSITION": 70}, {"NORMAL": 73, "POSITION": 72}, {"NORMAL": 75, "POSITION": 74}, {"NORMAL": 77, "POSITION": 76}, {"NORMAL": 79, "POSITION": 78}, {"NORMAL": 81, "POSITION": 80}, {"NORMAL": 83, "POSITION": 82}, {"NORMAL": 85, "POSITION": 84}, {"NORMAL": 87, "POSITION": 86}, {"NORMAL": 89, "POSITION": 88}, {"NORMAL": 91, "POSITION": 90}, {"NORMAL": 93, "POSITION": 92}, {"NORMAL": 95, "POSITION": 94}, {"NORMAL": 97, "POSITION": 96}, {"NORMAL": 99, "POSITION": 98}, {"NORMAL": 101, "POSITION": 100}, {"NORMAL": 103, "POSITION": 102}, {"NORMAL": 105, "POSITION": 104}, {"NORMAL": 107, "POSITION": 106}, {"NORMAL": 109, "POSITION": 108}, {"NORMAL": 111, "POSITION": 110}, {"NORMAL": 113, "POSITION": 112}, {"NORMAL": 115, "POSITION": 114}, {"NORMAL": 117, "POSITION": 116}, {"NORMAL": 119, "POSITION": 118}, {"NORMAL": 121, "POSITION": 120}, {"NORMAL": 123, "POSITION": 122}, {"NORMAL": 125, "POSITION": 124}, {"NORMAL": 127, "POSITION": 126}, {"NORMAL": 129, "POSITION": 128}, {"NORMAL": 131, "POSITION": 130}, {"NORMAL": 133, "POSITION": 132}, {"NORMAL": 135, "POSITION": 134}, {"NORMAL": 137, "POSITION": 136}, {"NORMAL": 139, "POSITION": 138}, {"NORMAL": 141, "POSITION": 140}, {"NORMAL": 143, "POSITION": 142}, {"NORMAL": 145, "POSITION": 144}, {"NORMAL": 147, "POSITION": 146}, {"NORMAL": 149, "POSITION": 148}, {"NORMAL": 151, "POSITION": 150}, {"NORMAL": 153, "POSITION": 152}, {"NORMAL": 155, "POSITION": 154}]}, {"attributes": {"JOINTS_0": 40, "NORMAL": 38, "POSITION": 37, "TEXCOORD_0": 39, "WEIGHTS_0": 41}, "extras": {"targetNames": ["Fcl_ALL_Neutral", "Fcl_ALL_Angry", "Fcl_ALL_Fun", "Fcl_ALL_Joy", "Fcl_ALL_Sorrow", "Fcl_ALL_Surprised", "Fcl_BRW_Angry", "Fcl_BRW_Fun", "Fcl_BRW_Joy", "Fcl_BRW_Sorrow", "Fcl_BRW_Surprised", "Fcl_EYE_Natural", "Fcl_EYE_Angry", "Fcl_EYE_Close", "Fcl_EYE_Close_R", "Fcl_EYE_Close_L", "Fcl_EYE_Fun", "Fcl_EYE_Joy", "Fcl_EYE_Joy_R", "Fcl_EYE_Joy_L", "Fcl_EYE_Sorrow", "Fcl_EYE_Surprised", "Fcl_EYE_Spread", "Fcl_EY<PERSON>_Iris_Hide", "Fcl_EYE_Highlight_Hide", "Fcl_MTH_Close", "Fcl_MTH_Up", "Fcl_MTH_Down", "Fcl_MTH_Angry", "Fcl_MTH_Small", "Fcl_MTH_Large", "Fcl_MTH_Neutral", "Fcl_MTH_Fun", "Fcl_MTH_Joy", "Fcl_MTH_Sorrow", "Fcl_MTH_Surprised", "Fcl_MTH_SkinFung", "Fcl_MTH_SkinFung_R", "Fcl_MTH_SkinFung_L", "Fcl_MTH_A", "Fcl_MTH_I", "Fcl_MTH_U", "Fcl_MTH_E", "Fcl_MTH_O", "Fcl_HA_Hide", "Fcl_HA_Fung1", "Fcl_HA_Fung1_Low", "Fcl_HA_Fung1_Up", "Fcl_HA_Fung2", "Fcl_HA_Fung2_Low", "Fcl_HA_Fung2_Up", "Fcl_HA_Fung3", "Fcl_HA_Fung3_Up", "Fcl_HA_Fung3_Low", "Fcl_HA_Short", "Fcl_HA_Short_Up", "Fcl_HA_Short_Low"]}, "indices": 36, "material": 5, "targets": [{"NORMAL": 43, "POSITION": 42}, {"NORMAL": 45, "POSITION": 44}, {"NORMAL": 47, "POSITION": 46}, {"NORMAL": 49, "POSITION": 48}, {"NORMAL": 51, "POSITION": 50}, {"NORMAL": 53, "POSITION": 52}, {"NORMAL": 55, "POSITION": 54}, {"NORMAL": 57, "POSITION": 56}, {"NORMAL": 59, "POSITION": 58}, {"NORMAL": 61, "POSITION": 60}, {"NORMAL": 63, "POSITION": 62}, {"NORMAL": 65, "POSITION": 64}, {"NORMAL": 67, "POSITION": 66}, {"NORMAL": 69, "POSITION": 68}, {"NORMAL": 71, "POSITION": 70}, {"NORMAL": 73, "POSITION": 72}, {"NORMAL": 75, "POSITION": 74}, {"NORMAL": 77, "POSITION": 76}, {"NORMAL": 79, "POSITION": 78}, {"NORMAL": 81, "POSITION": 80}, {"NORMAL": 83, "POSITION": 82}, {"NORMAL": 85, "POSITION": 84}, {"NORMAL": 87, "POSITION": 86}, {"NORMAL": 89, "POSITION": 88}, {"NORMAL": 91, "POSITION": 90}, {"NORMAL": 93, "POSITION": 92}, {"NORMAL": 95, "POSITION": 94}, {"NORMAL": 97, "POSITION": 96}, {"NORMAL": 99, "POSITION": 98}, {"NORMAL": 101, "POSITION": 100}, {"NORMAL": 103, "POSITION": 102}, {"NORMAL": 105, "POSITION": 104}, {"NORMAL": 107, "POSITION": 106}, {"NORMAL": 109, "POSITION": 108}, {"NORMAL": 111, "POSITION": 110}, {"NORMAL": 113, "POSITION": 112}, {"NORMAL": 115, "POSITION": 114}, {"NORMAL": 117, "POSITION": 116}, {"NORMAL": 119, "POSITION": 118}, {"NORMAL": 121, "POSITION": 120}, {"NORMAL": 123, "POSITION": 122}, {"NORMAL": 125, "POSITION": 124}, {"NORMAL": 127, "POSITION": 126}, {"NORMAL": 129, "POSITION": 128}, {"NORMAL": 131, "POSITION": 130}, {"NORMAL": 133, "POSITION": 132}, {"NORMAL": 135, "POSITION": 134}, {"NORMAL": 137, "POSITION": 136}, {"NORMAL": 139, "POSITION": 138}, {"NORMAL": 141, "POSITION": 140}, {"NORMAL": 143, "POSITION": 142}, {"NORMAL": 145, "POSITION": 144}, {"NORMAL": 147, "POSITION": 146}, {"NORMAL": 149, "POSITION": 148}, {"NORMAL": 151, "POSITION": 150}, {"NORMAL": 153, "POSITION": 152}, {"NORMAL": 155, "POSITION": 154}]}]}, {"name": "Body (merged).baked", "primitives": [{"attributes": {"JOINTS_0": 162, "NORMAL": 160, "POSITION": 159, "TEXCOORD_0": 161, "WEIGHTS_0": 163}, "indices": 156, "material": 6}, {"attributes": {"JOINTS_0": 162, "NORMAL": 160, "POSITION": 159, "TEXCOORD_0": 161, "WEIGHTS_0": 163}, "indices": 157, "material": 7}, {"attributes": {"JOINTS_0": 162, "NORMAL": 160, "POSITION": 159, "TEXCOORD_0": 161, "WEIGHTS_0": 163}, "indices": 158, "material": 8}]}, {"name": "Hair001 (merged).baked", "primitives": [{"attributes": {"JOINTS_0": 168, "NORMAL": 166, "POSITION": 165, "TEXCOORD_0": 167, "WEIGHTS_0": 169}, "indices": 164, "material": 9}]}], "nodes": [{"children": [1], "name": "Root", "translation": [-0.0, 0.0, 0.0]}, {"children": [2, 87, 98], "name": "J_Bip_C_Hips", "translation": [-6.423389597998153e-10, 0.8094256520271301, -0.003673759289085865]}, {"children": [3, 86], "name": "J_<PERSON>ip_C_Spine", "translation": [-2.09087502867078e-09, 0.049745380878448486, -0.011958413757383823]}, {"children": [4], "name": "J_Bip_C_Chest", "translation": [-5.094418220608077e-10, 0.10796588659286499, -0.002913679927587509]}, {"children": [5, 7, 9, 45, 65], "name": "J_Bip_C_UpperChest", "translation": [2.4465265369144618e-09, 0.10283654928207397, 0.013992493972182274]}, {"children": [6], "name": "J_Sec_L_Bust1", "translation": [-0.05118807032704353, -0.006932377815246582, -0.0658990889787674]}, {"name": "J_Sec_L_Bust2", "translation": [-0.008700434118509293, 0.0018897056579589844, -0.01530718058347702]}, {"children": [8], "name": "J_Sec_R_Bust1", "translation": [0.051188040524721146, -0.006932377815246582, -0.06589914858341217]}, {"name": "J_Sec_R_Bust2", "translation": [0.008700445294380188, 0.0018897056579589844, -0.015307188034057617]}, {"children": [10], "name": "J_Bip_C_Neck", "translation": [6.406925656676776e-09, 0.1263803243637085, 0.036643341183662415]}, {"children": [11, 12, 13, 22, 31, 34, 37, 40, 43, 44], "name": "J_<PERSON>ip_C_Head", "translation": [-1.513655867313446e-09, 0.06114387512207031, -0.007719317451119423]}, {"name": "<PERSON>_<PERSON><PERSON>_<PERSON>_FaceEye", "translation": [-0.011603696271777153, 0.057003140449523926, -0.023134075105190277]}, {"name": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>", "translation": [0.011603673920035362, 0.057003140449523926, -0.023134062066674232]}, {"children": [14], "name": "J_Sec_Hair1_01", "translation": [0.0820588618516922, 0.06416499614715576, -0.019053610041737556]}, {"children": [15], "name": "J_Sec_Hair2_01", "translation": [0.023703493177890778, -0.01872396469116211, 0.016896743327379227]}, {"children": [16], "name": "J_Sec_Hair3_01", "translation": [0.021038681268692017, -0.0030837059020996094, -0.03183184191584587]}, {"children": [17], "name": "J_Sec_Hair4_01", "translation": [-0.025160178542137146, -0.010054826736450195, -0.027198895812034607]}, {"children": [18], "name": "J_Sec_Hair5_01", "translation": [-0.02333589643239975, -0.023062944412231445, 0.02057502046227455]}, {"children": [19], "name": "J_Sec_Hair6_01", "translation": [0.01985001564025879, -0.007833242416381836, 0.03248593583703041]}, {"children": [20], "name": "J_Sec_Hair7_01", "translation": [0.03741452842950821, 0.00556182861328125, -0.008596519008278847]}, {"children": [21], "name": "J_Sec_Hair8_01", "translation": [-0.008192285895347595, -0.007918834686279297, -0.03499218448996544]}, {"name": "J_Sec_Hair9_01", "translation": [-0.029136836528778076, -0.02786886692047119, -0.0018516536802053452]}, {"children": [23], "name": "J_Sec_Hair1_02", "translation": [-0.08205888420343399, 0.06416499614715576, -0.01905350387096405]}, {"children": [24], "name": "J_Sec_Hair2_02", "translation": [-0.023703470826148987, -0.01872396469116211, 0.016896747052669525]}, {"children": [25], "name": "J_Sec_Hair3_02", "translation": [-0.02103869616985321, -0.0030837059020996094, -0.03183174878358841]}, {"children": [26], "name": "J_Sec_Hair4_02", "translation": [0.025160104036331177, -0.010054826736450195, -0.02719890885055065]}, {"children": [27], "name": "J_Sec_Hair5_02", "translation": [0.02333591878414154, -0.023062825202941895, 0.020574957132339478]}, {"children": [28], "name": "J_Sec_Hair6_02", "translation": [-0.019849970936775208, -0.007833242416381836, 0.03248591721057892]}, {"children": [29], "name": "J_Sec_Hair7_02", "translation": [-0.03741455078125, 0.00556182861328125, -0.008596448227763176]}, {"children": [30], "name": "J_Sec_Hair8_02", "translation": [0.008192270994186401, -0.007918953895568848, -0.03499213233590126]}, {"name": "J_Sec_Hair9_02", "translation": [0.029136814177036285, -0.02786874771118164, -0.0018516797572374344]}, {"children": [32], "name": "J_Sec_Hair1_03", "translation": [-0.09032701700925827, 0.13600409030914307, 0.06064122915267944]}, {"children": [33], "name": "J_Sec_Hair2_03", "translation": [-0.015203610062599182, -0.06198084354400635, -7.160007953643799e-05]}, {"name": "J_Sec_Hair3_03", "translation": [-0.005783118307590485, -0.06324708461761475, -0.01168879121541977]}, {"children": [35], "name": "J_Sec_Hair1_04", "translation": [-0.08158286660909653, 0.1869739294052124, 0.058281853795051575]}, {"children": [36], "name": "J_Sec_Hair2_04", "translation": [-0.035027943551540375, -0.004290342330932617, -0.02085215225815773]}, {"name": "J_Sec_Hair3_04", "translation": [-0.039226286113262177, -0.014118552207946777, -0.011213954538106918]}, {"children": [38], "name": "J_Sec_Hair1_05", "translation": [0.0815829485654831, 0.1869741678237915, 0.058281756937503815]}, {"children": [39], "name": "J_Sec_Hair2_05", "translation": [0.0350278839468956, -0.004290580749511719, -0.020852208137512207]}, {"name": "J_Sec_Hair3_05", "translation": [0.03922630846500397, -0.014118671417236328, -0.011214017868041992]}, {"children": [41], "name": "J_Sec_Hair1_06", "translation": [0.09032708406448364, 0.13600409030914307, 0.060641102492809296]}, {"children": [42], "name": "J_Sec_Hair2_06", "translation": [0.015203602612018585, -0.0619807243347168, -7.160753011703491e-05]}, {"name": "J_Sec_Hair3_06", "translation": [0.005783110857009888, -0.0632472038269043, -0.011688821017742157]}, {"children": [109], "name": "Parts.001.耳坠", "translation": [0.13980001211166382, -1.2940013408660889, -0.027799958363175392]}, {"children": [110], "name": "Parts.002.耳坠_1", "translation": [0.00019997016352135688, -1.294101357460022, -0.028399823233485222]}, {"children": [46], "name": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>er", "translation": [-0.02132064662873745, 0.10096633434295654, 0.028630521148443222]}, {"children": [47], "name": "J_Bip_L_UpperArm", "translation": [-0.07937373220920563, -0.011334061622619629, 1.1175870895385742e-08]}, {"children": [48, 64], "name": "J_Bip_L_LowerArm", "translation": [-0.2209724485874176, 0.0, 1.6763806343078613e-08]}, {"children": [49, 52, 55, 58, 61], "name": "<PERSON>_<PERSON><PERSON>_L_Hand", "translation": [-0.20894956588745117, 1.7881393432617188e-06, -0.0003668982535600662]}, {"children": [50], "name": "J_Bip_L_Index1", "translation": [-0.0540996789932251, 0.005584359169006348, -0.016296593472361565]}, {"children": [51], "name": "J_Bip_L_Index2", "translation": [-0.027480244636535645, 0.0, 0.0005357591435313225]}, {"name": "J_Bip_L_Index3", "translation": [-0.016903460025787354, -0.0004475116729736328, 0.000617247074842453]}, {"children": [53], "name": "J_Bip_L_Little1", "translation": [-0.05047941207885742, 0.005584359169006348, 0.024188358336687088]}, {"children": [54], "name": "J_Bip_L_Little2", "translation": [-0.02597707509994507, 0.0, -7.450580596923828e-09]}, {"name": "J_Bip_L_Little3", "translation": [-0.014971613883972168, 0.0, 3.725290298461914e-09]}, {"children": [56], "name": "J_Bip_L_Middle1", "translation": [-0.05483675003051758, 0.0055844783782958984, -0.0016970504075288773]}, {"children": [57], "name": "J_Bip_L_Middle2", "translation": [-0.03061521053314209, -1.1920928955078125e-07, 5.587935447692871e-09]}, {"name": "J_Bip_L_Middle3", "translation": [-0.01888740062713623, 0.0, 1.862645149230957e-09]}, {"children": [59], "name": "J_Bip_L_Ring1", "translation": [-0.054211974143981934, 0.005584359169006348, 0.011265333741903305]}, {"children": [60], "name": "J_Bip_L_Ring2", "translation": [-0.02839958667755127, 0.0, 1.862645149230957e-08]}, {"name": "J_Bip_L_Ring3", "translation": [-0.016389548778533936, 0.0, 1.4901161193847656e-08]}, {"children": [62], "name": "J_Bip_L_Thumb1", "translation": [-0.002911388874053955, -0.0075730085372924805, -0.01323455385863781]}, {"children": [63], "name": "J_Bip_L_Thumb2", "translation": [-0.029224812984466553, -0.0015606880187988281, -0.02508939430117607]}, {"name": "J_Bip_L_Thumb3", "translation": [-0.018589317798614502, -0.0007420778274536133, -0.01469331793487072]}, {"children": [111], "name": "Parts.013.左腕", "translation": [0.3369000554084778, -1.1920009851455688, -0.02649986557662487]}, {"children": [66], "name": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>er", "translation": [0.0213206484913826, 0.10096633434295654, 0.028630513697862625]}, {"children": [67], "name": "J_Bip_R_UpperArm", "translation": [0.07937372475862503, -0.011334061622619629, -1.6763806343078613e-08]}, {"children": [68, 85], "name": "J_Bip_R_LowerArm", "translation": [0.22097240388393402, 0.0, -5.960464477539063e-08]}, {"children": [69, 72, 75, 79, 82], "name": "J_<PERSON><PERSON>_R_Hand", "translation": [0.20894962549209595, 1.7881393432617188e-06, -0.00036697275936603546]}, {"children": [70], "name": "J_Bip_R_Index1", "translation": [0.0540996789932251, 0.005584359169006348, -0.016296615824103355]}, {"children": [71], "name": "J_Bip_R_Index2", "translation": [0.027480244636535645, 0.0, 0.0005357498303055763]}, {"name": "J_Bip_R_Index3", "translation": [0.016903460025787354, -0.0004475116729736328, 0.0006172414869070053]}, {"children": [73], "name": "J_Bip_R_Little1", "translation": [0.05047941207885742, 0.005584359169006348, 0.024188343435525894]}, {"children": [74], "name": "J_<PERSON><PERSON>_R_Little2", "translation": [0.02597707509994507, 0.0, -1.1175870895385742e-08]}, {"name": "J_Bip_R_Little3", "translation": [0.014971613883972168, 0.0, -1.1175870895385742e-08]}, {"children": [76, 78], "name": "J_Bip_R_Middle1", "translation": [0.05483675003051758, 0.0055844783782958984, -0.0016970615833997726]}, {"children": [77], "name": "J_Bip_R_Middle2", "translation": [0.03061521053314209, -1.1920928955078125e-07, -1.1175870895385742e-08]}, {"name": "J_Bip_R_Middle3", "translation": [0.01888740062713623, 0.0, -7.450580596923828e-09]}, {"children": [112], "name": "Parts.010.戒指", "translation": [-0.6064003109931946, -1.1987011432647705, -0.02300002984702587]}, {"children": [80], "name": "J_Bip_R_Ring1", "translation": [0.054211974143981934, 0.005584359169006348, 0.011265311390161514]}, {"children": [81], "name": "J_Bip_R_Ring2", "translation": [0.02839958667755127, 0.0, 1.862645149230957e-08]}, {"name": "J_Bip_R_Ring3", "translation": [0.016389548778533936, 0.0, 0.0]}, {"children": [83], "name": "J_Bip_R_Thumb1", "translation": [0.002911388874053955, -0.0075730085372924805, -0.013234554789960384]}, {"children": [84], "name": "J_Bip_R_Thumb2", "translation": [0.029224812984466553, -0.0015606880187988281, -0.025089403614401817]}, {"name": "J_Bip_R_Thumb3", "translation": [0.018589317798614502, -0.0007420778274536133, -0.014693322591483593]}, {"children": [113], "name": "Parts.014.右腕", "translation": [-0.33760008215904236, -1.1916007995605469, -0.02439977042376995]}, {"children": [114], "name": "Parts.003.腰带", "translation": [2.90246071621425e-09, -0.8769001960754395, 0.016600199043750763]}, {"children": [88, 90, 92, 94], "name": "J_Bip_L_UpperLeg", "translation": [-0.07345346361398697, -0.037819504737854004, 0.003470766358077526]}, {"children": [89], "name": "J_Sec_L_SkirtBack_01", "translation": [0.00197601318359375, -0.03395730257034302, 0.11135627329349518]}, {"name": "J_Sec_L_SkirtBack_end_01", "translation": [-0.00813639909029007, -0.07412606477737427, 0.013201743364334106]}, {"children": [91], "name": "J_Sec_L_SkirtFront_01", "translation": [0.007937870919704437, -0.04962742328643799, -0.11696965992450714]}, {"name": "J_Sec_L_SkirtFront_end_01", "translation": [-0.007944934070110321, -0.08130842447280884, 0.00042426586151123047]}, {"children": [93], "name": "J_Sec_L_SkirtSide_01", "translation": [-0.09608175605535507, -0.034034550189971924, -0.0034867250360548496]}, {"name": "J_Sec_L_SkirtSide_end_01", "translation": [-0.024755612015724182, -0.0803951621055603, 0.0101701645180583]}, {"children": [95, 97], "name": "J_Bip_L_LowerLeg", "translation": [1.4901161193847656e-08, -0.3154587745666504, 0.006601653527468443]}, {"children": [96], "name": "<PERSON>_<PERSON><PERSON>_<PERSON>_Foot", "translation": [2.9802322387695312e-08, -0.3674900233745575, 0.02216743491590023]}, {"name": "J_<PERSON>ip_L_ToeBase", "translation": [-7.450580596923828e-09, -0.05814582481980324, -0.10207071900367737]}, {"children": [115], "name": "Parts.015.左腿", "translation": [0.07540002465248108, -0.4643000364303589, -0.008099900558590889]}, {"children": [99, 101, 103, 105], "name": "J_Bip_R_UpperLeg", "translation": [0.07345344871282578, -0.037819504737854004, 0.003470742143690586]}, {"children": [100], "name": "J_Sec_R_SkirtBack_01", "translation": [-0.0019759461283683777, -0.03395730257034302, 0.11135627329349518]}, {"name": "J_Sec_R_SkirtBack_end_01", "translation": [0.006242506206035614, -0.07423019409179688, 0.013547360897064209]}, {"children": [102], "name": "J_Sec_R_SkirtFront_01", "translation": [-0.008147455751895905, -0.04964625835418701, -0.11704739928245544]}, {"name": "J_Sec_R_SkirtFront_end_01", "translation": [0.007701173424720764, -0.08132928609848022, 0.0003341361880302429]}, {"children": [104], "name": "J_Sec_R_SkirtSide_01", "translation": [0.09608177095651627, -0.03403443098068237, -0.0034867532085627317]}, {"name": "J_Sec_R_SkirtSide_end_01", "translation": [0.024755224585533142, -0.08039486408233643, 0.010170158930122852]}, {"children": [106, 108], "name": "J_Bip_R_LowerLeg", "translation": [7.450580596923828e-09, -0.31545883417129517, 0.006601654924452305]}, {"children": [107], "name": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>", "translation": [-0.0, -0.36749014258384705, 0.022167444229125977]}, {"name": "J_<PERSON>ip_R_ToeBase", "translation": [-2.2351741790771484e-08, -0.058145809918642044, -0.10207073390483856]}, {"children": [116], "name": "Parts.016.右腿", "translation": [-0.07530001550912857, -0.46610012650489807, -0.008999916724860668]}, {"mesh": 0, "name": "pSphere1", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [-0.07015390694141388, 1.3158149719238281, 0.026025548577308655]}, {"mesh": 1, "name": "pSphere1_1", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [-0.07246942818164825, 1.3183056116104126, 0.02602507546544075]}, {"mesh": 2, "name": "polySurface38", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [-1.3262033462524414e-06, 1.171593189239502, -0.07311785221099854]}, {"mesh": 3, "name": "polySurface36", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [0.6226674318313599, 1.1983494758605957, 0.022871874272823334]}, {"mesh": 4, "name": "polySurface40", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [-1.7583370208740234e-06, 1.1727699041366577, -0.06943661719560623]}, {"mesh": 5, "name": "polySurface33", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [0.000749182072468102, 0.945625364780426, -0.04134172201156616]}, {"mesh": 6, "name": "polySurface39", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [-0.054101452231407166, 0.15457387268543243, 0.040638890117406845]}, {"mesh": 7, "name": "polySurface35", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "translation": [0.07659041881561279, 0.14178623259067535, 0.030748091638088226]}, {"mesh": 8, "name": "Face", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "skin": 0}, {"mesh": 9, "name": "Body", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "skin": 1}, {"mesh": 10, "name": "Hair", "rotation": [0, 0, 0, 1], "scale": [1, 1, 1], "skin": 2}], "samplers": [{"magFilter": 9729, "minFilter": 9985, "wrapS": 10497, "wrapT": 10497}, {"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"nodes": [0, 117, 118, 119]}], "skins": [{"inverseBindMatrices": 0, "joints": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108], "skeleton": 0}, {"inverseBindMatrices": 0, "joints": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108], "skeleton": 0}, {"inverseBindMatrices": 0, "joints": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108], "skeleton": 0}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 1, "source": 2}, {"sampler": 1, "source": 3}, {"sampler": 1, "source": 4}, {"sampler": 1, "source": 5}, {"sampler": 1, "source": 6}, {"sampler": 1, "source": 7}, {"sampler": 1, "source": 8}, {"sampler": 1, "source": 9}, {"sampler": 1, "source": 10}, {"sampler": 1, "source": 11}, {"sampler": 1, "source": 12}, {"sampler": 1, "source": 13}, {"sampler": 1, "source": 14}]}