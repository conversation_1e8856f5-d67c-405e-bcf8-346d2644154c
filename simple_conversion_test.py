# -*- coding: utf-8 -*-
"""
简化的转换测试
"""

import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_vroid_vs_univrm():
    """对比VRoid和UniVRM的转换结果"""
    
    # 文件路径
    vroid_vrm_path = r"I:\AIV\豪德寺美弥子\豪德寺美弥子 Bubbles.vrm"
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    print("VRoid vs UniVRM 转换对比测试")
    print("=" * 50)
    
    # 测试VRoid文件
    print("\n1. 测试VRoid导出的VRM文件:")
    if os.path.exists(vroid_vrm_path):
        try:
            from mmd.VroidReader import VroidReader
            from service.VroidExportService import VroidExportService
            from module.MOptions import MOptionsDataSet
            
            # 读取VRoid VRM
            vroid_reader = VroidReader(vroid_vrm_path)
            vroid_model = vroid_reader.read_data()
            
            print(f"  ✓ VRoid VRM读取成功，节点数: {len(vroid_model.json_data.get('nodes', []))}")
            
            # 设置选项并转换
            options = MOptionsDataSet()
            options.vrm_model = vroid_model
            options.output_path = "test_vroid_output.pmx"
            options.version_name = "test_version"
            
            export_service = VroidExportService(options)
            result = export_service.create_model()
            
            if isinstance(result, tuple):
                pmx_model = result[0]
            else:
                pmx_model = result
            
            print(f"  ✓ VRoid转换成功")
            print(f"    - 顶点数: {len(pmx_model.vertices)}")
            print(f"    - 骨骼数: {len(pmx_model.bones)}")
            print(f"    - 材质数: {len(pmx_model.materials)}")
            
        except Exception as e:
            print(f"  ✗ VRoid转换失败: {str(e)}")
    else:
        print(f"  ✗ VRoid文件不存在: {vroid_vrm_path}")
    
    # 测试UniVRM文件
    print("\n2. 测试UniVRM导出的VRM文件:")
    if os.path.exists(univrm_vrm_path):
        try:
            from mmd.VroidReader import VroidReader
            from service.VroidExportService import VroidExportService
            from module.MOptions import MOptionsDataSet
            
            # 读取UniVRM VRM
            univrm_reader = VroidReader(univrm_vrm_path)
            univrm_model = univrm_reader.read_data()
            
            print(f"  ✓ UniVRM VRM读取成功，节点数: {len(univrm_model.json_data.get('nodes', []))}")
            
            # 设置选项并转换
            options = MOptionsDataSet()
            options.vrm_model = univrm_model
            options.output_path = "test_univrm_output.pmx"
            options.version_name = "test_version"
            
            export_service = VroidExportService(options)
            result = export_service.create_model()
            
            if isinstance(result, tuple):
                pmx_model = result[0]
            else:
                pmx_model = result
            
            print(f"  ✓ UniVRM转换成功")
            print(f"    - 顶点数: {len(pmx_model.vertices)}")
            print(f"    - 骨骼数: {len(pmx_model.bones)}")
            print(f"    - 材质数: {len(pmx_model.materials)}")
            
            # 检查关键骨骼
            bone_names = [bone.name for bone in pmx_model.bones]
            key_bones = ["全ての親", "センター", "上半身", "下半身", "頭", "首"]
            found_key_bones = [bone for bone in key_bones if bone in bone_names]
            
            print(f"    - 关键骨骼: {len(found_key_bones)}/{len(key_bones)} 个")
            if found_key_bones:
                print(f"      找到: {', '.join(found_key_bones)}")
            
            missing_key_bones = [bone for bone in key_bones if bone not in bone_names]
            if missing_key_bones:
                print(f"      缺失: {', '.join(missing_key_bones)}")
            
        except Exception as e:
            print(f"  ✗ UniVRM转换失败: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print(f"  ✗ UniVRM文件不存在: {univrm_vrm_path}")
    
    print("\n测试完成")

if __name__ == "__main__":
    test_vroid_vs_univrm()
