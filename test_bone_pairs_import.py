#!/usr/bin/env python3
"""
测试bone_pairs.py导入和功能
"""

import sys
import os
sys.path.append('src')

def test_bone_pairs_import():
    """测试bone_pairs模块导入"""
    try:
        from config.bone_pairs import VROID_BONE_PAIRS, UNIVRM_BONE_PAIRS, get_bone_pairs
        
        print("✓ 成功导入bone_pairs模块")
        print(f"✓ VROID_BONE_PAIRS包含{len(VROID_BONE_PAIRS)}个骨骼定义")
        print(f"✓ UNIVRM_BONE_PAIRS包含{len(UNIVRM_BONE_PAIRS)}个骨骼定义")
        
        # 测试get_bone_pairs函数
        vroid_pairs = get_bone_pairs("VRoid")
        univrm_pairs = get_bone_pairs("UniVRM")
        
        print(f"✓ get_bone_pairs('VRoid')返回{len(vroid_pairs)}个骨骼")
        print(f"✓ get_bone_pairs('UniVRM')返回{len(univrm_pairs)}个骨骼")
        
        # 检查一些关键骨骼
        vroid_keys = ["J_Bip_C_Hips", "J_Bip_L_Hand", "J_Bip_L_Thumb1"]
        univrm_keys = ["Hips", "hand_L", "ThumbProximal_L"]
        
        print("\n检查VRoid关键骨骼:")
        for key in vroid_keys:
            if key in vroid_pairs:
                print(f"✓ {key}: {vroid_pairs[key]['name']}")
            else:
                print(f"✗ {key}: 未找到")
        
        print("\n检查UniVRM关键骨骼:")
        for key in univrm_keys:
            if key in univrm_pairs:
                print(f"✓ {key}: {univrm_pairs[key]['name']}")
            else:
                print(f"✗ {key}: 未找到")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_vroid_service_import():
    """测试VroidExportService导入"""
    try:
        # 直接导入模块而不是类
        import service.VroidExportService as vroid_service
        print("✓ 成功导入VroidExportService模块")

        # 检查是否能正确导入bone_pairs
        if hasattr(vroid_service, 'VROID_BONE_PAIRS'):
            print("✓ VroidExportService成功导入VROID_BONE_PAIRS")
        if hasattr(vroid_service, 'UNIVRM_BONE_PAIRS'):
            print("✓ VroidExportService成功导入UNIVRM_BONE_PAIRS")
        if hasattr(vroid_service, 'get_bone_pairs'):
            print("✓ VroidExportService成功导入get_bone_pairs函数")

        return True

    except ImportError as e:
        print(f"✗ VroidExportService导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ VroidExportService测试失败: {e}")
        return False

def main():
    print("=== 测试bone_pairs模块 ===")
    success1 = test_bone_pairs_import()
    
    print("\n=== 测试VroidExportService导入 ===")
    success2 = test_vroid_service_import()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    main()
