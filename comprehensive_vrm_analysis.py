# -*- coding: utf-8 -*-
"""
全面分析VRoid Studio vs UniVRM的数据结构差异
"""

import sys
import os
import json
import logging
from pathlib import Path

# 设置日志输出到文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vrm_analysis.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def analyze_vrm_structure(vrm_path, output_prefix):
    """分析VRM文件的完整数据结构"""
    
    logger.info(f"开始分析VRM文件: {vrm_path}")
    
    try:
        from mmd.VroidReader import VroidReader
        
        # 读取VRM文件
        vroid_reader = VroidReader(vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model or not vrm_model.json_data:
            logger.error(f"无法读取VRM文件: {vrm_path}")
            return False
        
        json_data = vrm_model.json_data
        
        # 输出完整的JSON结构分析
        analysis_file = f"{output_prefix}_complete_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"完整JSON数据已保存到: {analysis_file}")
        
        # 分析各个关键部分
        analyze_nodes(json_data, output_prefix)
        analyze_meshes(json_data, output_prefix)
        analyze_materials(json_data, output_prefix)
        analyze_textures(json_data, output_prefix)
        analyze_accessors(json_data, output_prefix)
        analyze_buffer_views(json_data, output_prefix)
        analyze_vrm_extensions(json_data, output_prefix)
        analyze_animations(json_data, output_prefix)
        
        return True
        
    except Exception as e:
        logger.error(f"分析VRM文件时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_nodes(json_data, output_prefix):
    """分析节点结构"""
    logger.info("分析节点结构...")
    
    nodes = json_data.get('nodes', [])
    node_analysis = {
        'total_count': len(nodes),
        'nodes_with_mesh': [],
        'nodes_with_skin': [],
        'nodes_with_children': [],
        'bone_nodes': [],
        'mesh_nodes': [],
        'root_nodes': []
    }
    
    for idx, node in enumerate(nodes):
        node_name = node.get('name', f'Node_{idx}')
        
        if 'mesh' in node:
            node_analysis['nodes_with_mesh'].append({
                'index': idx,
                'name': node_name,
                'mesh_index': node['mesh']
            })
            node_analysis['mesh_nodes'].append(node_name)
        
        if 'skin' in node:
            node_analysis['nodes_with_skin'].append({
                'index': idx,
                'name': node_name,
                'skin_index': node['skin']
            })
        
        if 'children' in node:
            node_analysis['nodes_with_children'].append({
                'index': idx,
                'name': node_name,
                'children_count': len(node['children'])
            })
        
        # 判断是否为根节点（没有被其他节点引用为子节点）
        is_root = True
        for other_node in nodes:
            if 'children' in other_node and idx in other_node['children']:
                is_root = False
                break
        
        if is_root:
            node_analysis['root_nodes'].append({
                'index': idx,
                'name': node_name
            })
    
    # 保存节点分析结果
    with open(f"{output_prefix}_nodes_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(node_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"节点分析完成: {len(nodes)}个节点, {len(node_analysis['mesh_nodes'])}个网格节点")

def analyze_meshes(json_data, output_prefix):
    """分析网格结构"""
    logger.info("分析网格结构...")
    
    meshes = json_data.get('meshes', [])
    mesh_analysis = {
        'total_count': len(meshes),
        'meshes': []
    }
    
    for idx, mesh in enumerate(meshes):
        mesh_info = {
            'index': idx,
            'name': mesh.get('name', f'Mesh_{idx}'),
            'primitives_count': len(mesh.get('primitives', [])),
            'primitives': []
        }
        
        for prim_idx, primitive in enumerate(mesh.get('primitives', [])):
            prim_info = {
                'index': prim_idx,
                'attributes': primitive.get('attributes', {}),
                'material': primitive.get('material'),
                'indices': primitive.get('indices'),
                'mode': primitive.get('mode', 4)  # 默认为TRIANGLES
            }
            mesh_info['primitives'].append(prim_info)
        
        mesh_analysis['meshes'].append(mesh_info)
    
    # 保存网格分析结果
    with open(f"{output_prefix}_meshes_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(mesh_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"网格分析完成: {len(meshes)}个网格")

def analyze_materials(json_data, output_prefix):
    """分析材质结构"""
    logger.info("分析材质结构...")
    
    materials = json_data.get('materials', [])
    material_analysis = {
        'total_count': len(materials),
        'materials': []
    }
    
    for idx, material in enumerate(materials):
        mat_info = {
            'index': idx,
            'name': material.get('name', f'Material_{idx}'),
            'pbr_metallic_roughness': material.get('pbrMetallicRoughness', {}),
            'normal_texture': material.get('normalTexture'),
            'occlusion_texture': material.get('occlusionTexture'),
            'emissive_texture': material.get('emissiveTexture'),
            'emissive_factor': material.get('emissiveFactor'),
            'alpha_mode': material.get('alphaMode', 'OPAQUE'),
            'alpha_cutoff': material.get('alphaCutoff'),
            'double_sided': material.get('doubleSided', False),
            'extensions': material.get('extensions', {})
        }
        
        material_analysis['materials'].append(mat_info)
    
    # 保存材质分析结果
    with open(f"{output_prefix}_materials_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(material_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"材质分析完成: {len(materials)}个材质")

def analyze_textures(json_data, output_prefix):
    """分析纹理结构"""
    logger.info("分析纹理结构...")
    
    textures = json_data.get('textures', [])
    images = json_data.get('images', [])
    
    texture_analysis = {
        'textures_count': len(textures),
        'images_count': len(images),
        'textures': [],
        'images': []
    }
    
    for idx, texture in enumerate(textures):
        tex_info = {
            'index': idx,
            'source': texture.get('source'),
            'sampler': texture.get('sampler')
        }
        texture_analysis['textures'].append(tex_info)
    
    for idx, image in enumerate(images):
        img_info = {
            'index': idx,
            'name': image.get('name'),
            'uri': image.get('uri'),
            'buffer_view': image.get('bufferView'),
            'mime_type': image.get('mimeType')
        }
        texture_analysis['images'].append(img_info)
    
    # 保存纹理分析结果
    with open(f"{output_prefix}_textures_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(texture_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"纹理分析完成: {len(textures)}个纹理, {len(images)}个图像")

def analyze_accessors(json_data, output_prefix):
    """分析访问器结构"""
    logger.info("分析访问器结构...")
    
    accessors = json_data.get('accessors', [])
    accessor_analysis = {
        'total_count': len(accessors),
        'accessors': []
    }
    
    for idx, accessor in enumerate(accessors):
        acc_info = {
            'index': idx,
            'buffer_view': accessor.get('bufferView'),
            'byte_offset': accessor.get('byteOffset', 0),
            'component_type': accessor.get('componentType'),
            'count': accessor.get('count'),
            'type': accessor.get('type'),
            'max': accessor.get('max'),
            'min': accessor.get('min'),
            'normalized': accessor.get('normalized', False)
        }
        accessor_analysis['accessors'].append(acc_info)
    
    # 保存访问器分析结果
    with open(f"{output_prefix}_accessors_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(accessor_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"访问器分析完成: {len(accessors)}个访问器")

def analyze_buffer_views(json_data, output_prefix):
    """分析缓冲区视图结构"""
    logger.info("分析缓冲区视图结构...")
    
    buffer_views = json_data.get('bufferViews', [])
    buffers = json_data.get('buffers', [])
    
    buffer_analysis = {
        'buffer_views_count': len(buffer_views),
        'buffers_count': len(buffers),
        'buffer_views': [],
        'buffers': []
    }
    
    for idx, buffer_view in enumerate(buffer_views):
        bv_info = {
            'index': idx,
            'buffer': buffer_view.get('buffer'),
            'byte_offset': buffer_view.get('byteOffset', 0),
            'byte_length': buffer_view.get('byteLength'),
            'byte_stride': buffer_view.get('byteStride'),
            'target': buffer_view.get('target')
        }
        buffer_analysis['buffer_views'].append(bv_info)
    
    for idx, buffer in enumerate(buffers):
        buf_info = {
            'index': idx,
            'byte_length': buffer.get('byteLength'),
            'uri': buffer.get('uri')
        }
        buffer_analysis['buffers'].append(buf_info)
    
    # 保存缓冲区分析结果
    with open(f"{output_prefix}_buffers_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(buffer_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"缓冲区分析完成: {len(buffer_views)}个缓冲区视图, {len(buffers)}个缓冲区")

def analyze_vrm_extensions(json_data, output_prefix):
    """分析VRM扩展结构"""
    logger.info("分析VRM扩展结构...")
    
    extensions = json_data.get('extensions', {})
    vrm_ext = extensions.get('VRM', {})
    
    vrm_analysis = {
        'exporter_version': vrm_ext.get('exporterVersion'),
        'spec_version': vrm_ext.get('specVersion'),
        'meta': vrm_ext.get('meta', {}),
        'humanoid': vrm_ext.get('humanoid', {}),
        'blend_shape_master': vrm_ext.get('blendShapeMaster', {}),
        'secondary_animation': vrm_ext.get('secondaryAnimation', {}),
        'material_properties': vrm_ext.get('materialProperties', [])
    }
    
    # 保存VRM扩展分析结果
    with open(f"{output_prefix}_vrm_extensions_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(vrm_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"VRM扩展分析完成")

def analyze_animations(json_data, output_prefix):
    """分析动画结构"""
    logger.info("分析动画结构...")
    
    animations = json_data.get('animations', [])
    animation_analysis = {
        'total_count': len(animations),
        'animations': []
    }
    
    for idx, animation in enumerate(animations):
        anim_info = {
            'index': idx,
            'name': animation.get('name', f'Animation_{idx}'),
            'channels_count': len(animation.get('channels', [])),
            'samplers_count': len(animation.get('samplers', []))
        }
        animation_analysis['animations'].append(anim_info)
    
    # 保存动画分析结果
    with open(f"{output_prefix}_animations_analysis.json", 'w', encoding='utf-8') as f:
        json.dump(animation_analysis, f, ensure_ascii=False, indent=2)
    
    logger.info(f"动画分析完成: {len(animations)}个动画")

def main():
    """主函数"""
    logger.info("开始全面VRM数据结构分析")
    
    # VRoid Studio导出的VRM文件
    vroid_vrm_path = r"I:\AIV\豪德寺美弥子\豪德寺美弥子 Bubbles.vrm"
    
    # UniVRM导出的VRM文件
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    # 分析VRoid Studio文件
    if os.path.exists(vroid_vrm_path):
        logger.info("分析VRoid Studio导出的VRM文件...")
        analyze_vrm_structure(vroid_vrm_path, "vroid_studio")
    else:
        logger.warning(f"VRoid Studio文件不存在: {vroid_vrm_path}")
    
    # 分析UniVRM文件
    if os.path.exists(univrm_vrm_path):
        logger.info("分析UniVRM导出的VRM文件...")
        analyze_vrm_structure(univrm_vrm_path, "univrm")
    else:
        logger.warning(f"UniVRM文件不存在: {univrm_vrm_path}")
    
    logger.info("全面分析完成，请查看生成的分析文件")

if __name__ == "__main__":
    main()
