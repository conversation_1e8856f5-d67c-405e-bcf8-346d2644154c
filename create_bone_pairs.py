#!/usr/bin/env python3
"""
脚本用于从VroidExportService.py提取完整的BONE_PAIRS定义
并创建对应的UniVRM版本
"""

import re
import os

def extract_vroid_bone_pairs():
    """从VroidExportService.py提取VROID_BONE_PAIRS"""
    with open('src/service/VroidExportService.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到VROID_BONE_PAIRS的开始和结束
    start_pattern = r'VROID_BONE_PAIRS\s*=\s*{'
    end_pattern = r'^}'
    
    start_match = re.search(start_pattern, content, re.MULTILINE)
    if not start_match:
        print("未找到VROID_BONE_PAIRS定义")
        return None
    
    start_pos = start_match.start()
    
    # 从开始位置查找匹配的结束大括号
    lines = content[start_pos:].split('\n')
    brace_count = 0
    bone_pairs_lines = []
    
    for line in lines:
        bone_pairs_lines.append(line)
        brace_count += line.count('{') - line.count('}')
        if brace_count == 0 and line.strip() == '}':
            break
    
    return '\n'.join(bone_pairs_lines)

def create_univrm_bone_pairs(vroid_content):
    """基于VROID_BONE_PAIRS创建UNIVRM_BONE_PAIRS"""
    # VRoid到UniVRM的骨骼名称映射
    # 基于实际UniVRM导出的VRM文件中的骨骼名称
    bone_mapping = {
        'J_Bip_C_Hips': 'Hips',
        'J_Bip_C_Spine': 'Spine',
        'J_Bip_C_Spine2': 'Chest',
        'J_Bip_C_Chest': 'Chest',  # UniVRM只有一个Chest
        'J_Bip_C_UpperChest': 'Chest',  # 映射到同一个Chest
        'J_Bip_C_Neck': 'Neck',
        'J_Bip_C_Head': 'Head',
        'J_Bip_L_Shoulder': 'shoulder_L',
        'J_Bip_L_UpperArm': 'upperArm_L',
        'J_Bip_L_LowerArm': 'lowerArm_L',
        'J_Bip_L_Hand': 'hand_L',
        'J_Bip_R_Shoulder': 'shoulder_R',
        'J_Bip_R_UpperArm': 'upperArm_R',
        'J_Bip_R_LowerArm': 'lowerArm_R',
        'J_Bip_R_Hand': 'hand_R',
        'J_Bip_L_UpperLeg': 'upperLeg_L',
        'J_Bip_L_LowerLeg': 'lowerLeg_L',
        'J_Bip_L_Foot': 'foot_L',
        'J_Bip_L_ToeBase': 'toes_L',
        'J_Bip_R_UpperLeg': 'upperLeg_R',
        'J_Bip_R_LowerLeg': 'lowerLeg_R',
        'J_Bip_R_Foot': 'foot_R',
        'J_Bip_R_ToeBase': 'toes_R',
        # 手指骨骼映射 - 基于实际UniVRM骨骼名称
        'J_Bip_L_Thumb1': 'ThumbProximal_L',
        'J_Bip_L_Thumb2': 'ThumbIntermediate_L',
        'J_Bip_L_Thumb3': 'ThumbDistal_L',
        'J_Bip_L_Index1': 'IndexProximal_L',
        'J_Bip_L_Index2': 'IndexIntermediate_L',
        'J_Bip_L_Index3': 'IndexDistal_L',
        'J_Bip_L_Middle1': 'MiddleProximal_L',
        'J_Bip_L_Middle2': 'MiddleIntermediate_L',
        'J_Bip_L_Middle3': 'MiddleDistal_L',
        'J_Bip_L_Ring1': 'RingProximal_L',
        'J_Bip_L_Ring2': 'RingIntermediate_L',
        'J_Bip_L_Ring3': 'RingDistal_L',
        'J_Bip_L_Little1': 'LittleProximal_L',
        'J_Bip_L_Little2': 'LittleIntermediate_L',
        'J_Bip_L_Little3': 'LittleDistal_L',
        'J_Bip_R_Thumb1': 'ThumbProximal_R',
        'J_Bip_R_Thumb2': 'ThumbIntermediate_R',
        'J_Bip_R_Thumb3': 'ThumbDistal_R',
        'J_Bip_R_Index1': 'IndexProximal_R',
        'J_Bip_R_Index2': 'IndexIntermediate_R',
        'J_Bip_R_Index3': 'IndexDistal_R',
        'J_Bip_R_Middle1': 'MiddleProximal_R',
        'J_Bip_R_Middle2': 'MiddleIntermediate_R',
        'J_Bip_R_Middle3': 'MiddleDistal_R',
        'J_Bip_R_Ring1': 'RingProximal_R',
        'J_Bip_R_Ring2': 'RingIntermediate_R',
        'J_Bip_R_Ring3': 'RingDistal_R',
        'J_Bip_R_Little1': 'LittleProximal_R',
        'J_Bip_R_Little2': 'LittleIntermediate_R',
        'J_Bip_R_Little3': 'LittleDistal_R',
        # 眼部骨骼
        'J_Adj_L_FaceEye': 'LeftEye',
        'J_Adj_R_FaceEye': 'RightEye',
    }
    
    # 替换骨骼名称
    univrm_content = vroid_content.replace('VROID_BONE_PAIRS', 'UNIVRM_BONE_PAIRS')
    
    for vroid_name, univrm_name in bone_mapping.items():
        # 替换键名
        univrm_content = re.sub(f'"{vroid_name}":', f'"{univrm_name}":', univrm_content)
        # 替换parent引用
        univrm_content = re.sub(f'"parent":\s*"{vroid_name}"', f'"parent": "{univrm_name}"', univrm_content)
        # 替换tail引用
        univrm_content = re.sub(f'"tail":\s*"{vroid_name}"', f'"tail": "{univrm_name}"', univrm_content)
    
    return univrm_content

def main():
    print("提取VROID_BONE_PAIRS...")
    vroid_content = extract_vroid_bone_pairs()
    
    if not vroid_content:
        print("提取失败")
        return
    
    print(f"提取成功，共{len(vroid_content.split('    '))} 行")
    
    print("创建UNIVRM_BONE_PAIRS...")
    univrm_content = create_univrm_bone_pairs(vroid_content)
    
    # 创建完整的bone_pairs.py文件
    full_content = f'''"""
VRM骨骼映射配置文件
包含VRoid Studio和UniVRM两种不同的骨骼命名规范的BONE_PAIRS定义
"""

from mmd.MVector3D import MVector3D

# VRoid Studio专用的BONE_PAIRS
{vroid_content}

# UniVRM专用的BONE_PAIRS (使用UniVRM骨骼命名)
{univrm_content}

# 根据VRM类型选择对应的BONE_PAIRS
def get_bone_pairs(vrm_type):
    """根据VRM导出器类型返回对应的BONE_PAIRS"""
    if vrm_type == "UniVRM":
        return UNIVRM_BONE_PAIRS
    else:
        return VROID_BONE_PAIRS

# 为了向后兼容，保持原有的BONE_PAIRS变量
BONE_PAIRS = VROID_BONE_PAIRS
'''
    
    # 写入文件
    with open('src/config/bone_pairs.py', 'w', encoding='utf-8') as f:
        f.write(full_content)
    
    print("bone_pairs.py创建完成！")
    
    # 统计骨骼数量
    vroid_count = len(re.findall(r'^\s*"[^"]+"\s*:\s*{', vroid_content, re.MULTILINE))
    univrm_count = len(re.findall(r'^\s*"[^"]+"\s*:\s*{', univrm_content, re.MULTILINE))
    
    print(f"VROID_BONE_PAIRS: {vroid_count} 个骨骼")
    print(f"UNIVRM_BONE_PAIRS: {univrm_count} 个骨骼")

if __name__ == "__main__":
    main()
