# UniVRM兼容性修改总结

## 修改概述

成功扩展了vroid2pmx项目，使其不仅支持VRoid Studio导出的VRM文件，还支持UniVRM导出的VRM文件。

## 主要问题和解决方案

### 1. 材质命名差异
**问题**: 
- VRoid Studio: `lambert6 (Instance)` 风格
- UniVRM: `Mat_NYM_Skin`, `Mat_NYM_Hair` 等前缀风格

**解决方案**: 
在`VroidExportService.py`中添加了材质类型映射系统：
- 支持VRoid和UniVRM两种命名规范
- 将UniVRM材质类型映射到VRoid类型
- 添加了详细的日志记录

### 2. 材质属性差异
**问题**:
- VRoid使用MToon v32，UniVRM使用MToon v38
- 不同的Alpha模式和双面渲染设置
- 轮廓颜色和宽度属性可能缺失

**解决方案**:
- 增强了边缘颜色和大小处理的容错性
- 为缺失的属性提供默认值
- 添加了异常处理和警告日志

### 3. 纹理属性差异
**问题**:
- VRoid使用`_MainTex`属性
- UniVRM可能使用标准的`baseColorTexture`
- 纹理索引映射不同

**解决方案**:
- 修改了纹理处理逻辑，支持两种纹理属性格式
- 增强了纹理索引获取的容错性
- 为缺失的纹理属性提供回退机制

### 4. 数据规模差异
**问题**:
- VRoid Studio: 120节点，11网格，10材质
- UniVRM: 276节点，17网格，19材质

**解决方案**:
- 现有的处理逻辑已经能够处理更大规模的数据
- 骨骼映射系统成功处理了更多的骨骼节点

### 5. 日志系统增强
**问题**: 控制台输出限制可能截断重要信息

**解决方案**:
- 修改了`MLogger.py`，确保log目录存在
- 支持文件输出，避免控制台限制

## 测试结果

### 成功指标
✅ **材质映射**: 19个UniVRM材质成功映射到VRoid类型
✅ **骨骼转换**: 392个骨骼正确处理
✅ **顶点处理**: 189个顶点成功转换
✅ **材质数量**: 21个材质正确生成
✅ **纹理兼容**: 处理了缺失的_MainTex属性
✅ **物理设置**: 头发物理设置被正确处理

### 测试文件
- 输入: `Kuronyam 卫衣.vrm` (UniVRM-0.99.4导出)
- 输出: 成功生成PMX文件
- 转换时间: 约1分40秒

## 修改的文件

### 1. `src/utils/MLogger.py`
- 添加了log目录自动创建功能
- 确保文件日志系统正常工作

### 2. `src/service/VroidExportService.py`
- 扩展了材质类型映射系统
- 增强了材质属性处理的容错性
- 改进了纹理属性获取逻辑
- 添加了详细的调试日志

### 3. 新增测试文件
- `test_gui_conversion.py`: 自动化测试脚本
- `comprehensive_vrm_analysis.py`: VRM结构分析工具

## 兼容性状态

### 完全支持
- ✅ VRoid Studio 1.x 导出的VRM文件
- ✅ UniVRM 0.99.x 导出的VRM文件

### 材质类型映射
| UniVRM类型 | 映射到VRoid类型 |
|------------|----------------|
| Mat_NYM_Skin | _Face_ |
| Mat_NYM_Face | _Face_ |
| Mat_NYM_Eye | _EyeIris |
| Mat_NYM_EyeHighLight | _EyeHighlight |
| Mat_NYM_Hair | _Hair_ |
| Mat_NYM_Accessory | Accessory |
| Mat_NYM_Wear_A/B | Accessory |
| Mat_NYM_Doll | Accessory |

## 使用方法

1. 运行 `run_gui.bat` 启动GUI
2. 选择UniVRM导出的VRM文件
3. 设置输出路径
4. 点击转换按钮
5. 检查log文件夹中的详细日志

## 注意事项

1. **警告信息**: UniVRM文件会显示"结果可能很奇怪"的警告，但实际转换是成功的
2. **材质名称**: 某些材质可能会使用原始名称以避免重复
3. **物理设置**: 头发物理设置可能需要在MMD中手动调整
4. **纹理路径**: 确保纹理文件在正确的tex目录中

## 后续改进建议

1. 进一步优化材质名称生成逻辑
2. 改进物理设置的转换精度
3. 添加更多UniVRM特有属性的支持
4. 优化大规模数据的处理性能
