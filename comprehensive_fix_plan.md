# VRoid vs UniVRM 全面兼容性修改方案

## 发现的关键差异

### 1. 数据规模差异
- **VRoid Studio**: 120个节点, 11个网格, 10个材质, 15个纹理, 170个访问器
- **UniVRM**: 276个节点, 17个网格, 19个材质, 17个纹理, 371个访问器

### 2. 材质命名差异
- **VRoid Studio**: `lambert6 (Instance)`, `lambert3 (Instance)` 等
- **UniVRM**: `Mat_NYM_Skin`, `Mat_NYM_AccessoryBoots` 等

### 3. 材质属性差异
- **Alpha模式**: VRoid使用`MASK`，UniVRM使用`OPAQUE`
- **双面渲染**: VRoid为`false`，UniVRM为`true`
- **MToon版本**: VRoid使用v32，UniVRM使用v38

### 4. 骨骼节点索引差异
- **VRoid**: hips=1, spine=2, chest=3, neck=9, head=10
- **UniVRM**: hips=1, spine=22, neck=45, head=46

## 需要修改的文件和模块

### 1. 核心服务文件
- `src/service/VroidExportService.py` - 主要转换逻辑
- `src/service/ConvertService.py` - 转换服务基类
- `src/service/ConvertMaterialService.py` - 材质转换服务

### 2. 数据模型文件
- `src/mmd/VroidReader.py` - VRM文件读取器
- `src/mmd/PmxData.py` - PMX数据结构
- `src/pmx/PmxModel.py` - PMX模型类

### 3. 工具类文件
- `src/utils/MLogger.py` - 日志工具
- `src/utils/MServiceUtils.py` - 服务工具类

### 4. 配置文件
- `src/module/MOptions.py` - 选项配置

## 修改策略

### 1. 材质处理增强
- 支持UniVRM的材质命名规范
- 处理不同的Alpha模式和双面渲染
- 兼容不同版本的MToon材质

### 2. 网格数据处理
- 处理更大规模的网格数据
- 优化访问器和缓冲区视图的处理
- 支持更多的图元类型

### 3. 纹理处理改进
- 支持不同的纹理格式和命名
- 处理纹理坐标差异
- 优化纹理内存使用

### 4. 日志系统改进
- 输出到文件避免控制台限制
- 分级日志记录
- 详细的错误追踪

### 5. 错误处理增强
- 更好的异常捕获和处理
- 回退机制
- 用户友好的错误信息

## 实施步骤

1. **设置日志系统** - 修改MLogger.py支持文件输出
2. **增强材质处理** - 修改ConvertMaterialService.py
3. **改进骨骼映射** - 扩展VroidExportService.py中的骨骼处理
4. **优化网格处理** - 改进网格数据转换逻辑
5. **测试和调试** - 使用run_gui.bat进行测试
6. **性能优化** - 优化大规模数据处理

## 预期效果

- 完全支持UniVRM导出的VRM文件
- 正确处理材质、纹理、刚体等所有数据
- 生成完整可用的PMX模型
- 提供详细的转换日志
- 保持与VRoid Studio文件的兼容性
