# -*- coding: utf-8 -*-
"""
VRM骨骼结构分析工具
分析VRoid Studio和UniVRM导出的VRM文件的骨骼命名差异
"""

import json
import struct
import os
from pathlib import Path

class VRMAnalyzer:
    def __init__(self):
        self.offset = 0
        self.buffer = None
    
    def unpack(self, size, format_str):
        """从buffer中解包数据"""
        if self.offset + size > len(self.buffer):
            raise ValueError(f"Buffer overflow: trying to read {size} bytes at offset {self.offset}, buffer size is {len(self.buffer)}")
        data = struct.unpack(format_str, self.buffer[self.offset:self.offset + size])
        self.offset += size
        return data[0] if len(data) == 1 else data
    
    def read_text(self, size):
        """读取文本数据"""
        text_bytes = self.buffer[self.offset:self.offset + size]
        self.offset += size
        return text_bytes.decode('utf-8')
    
    def load_vrm_json(self, vrm_path):
        """加载VRM文件的JSON数据"""
        with open(vrm_path, "rb") as f:
            self.buffer = f.read()
            self.offset = 0

            # 读取签名 (12字节)
            signature = self.unpack(12, "12s")
            print(f"VRM签名: {signature}")

            # 读取JSON chunk长度 (4字节，小端序)
            json_buf_size = self.unpack(4, "<L")
            print(f"JSON数据大小: {json_buf_size}")

            # 跳过chunk类型 (4字节)
            chunk_type = self.unpack(4, "4s")
            print(f"Chunk类型: {chunk_type}")

            # 读取JSON文本
            json_text = self.read_text(json_buf_size)

            return json.loads(json_text)
    
    def extract_bone_info(self, json_data):
        """提取骨骼信息"""
        bone_info = {
            'exporter': json_data.get('extensions', {}).get('VRM', {}).get('exporterVersion', 'Unknown'),
            'nodes': [],
            'bone_hierarchy': {},
            'humanoid_bones': {}
        }
        
        # 提取节点信息
        if 'nodes' in json_data:
            for idx, node in enumerate(json_data['nodes']):
                node_info = {
                    'index': idx,
                    'name': node.get('name', f'Node_{idx}'),
                    'translation': node.get('translation', [0, 0, 0]),
                    'rotation': node.get('rotation', [0, 0, 0, 1]),
                    'scale': node.get('scale', [1, 1, 1]),
                    'children': node.get('children', []),
                    'parent': -1  # 稍后填充
                }
                bone_info['nodes'].append(node_info)
        
        # 建立父子关系
        for node in bone_info['nodes']:
            for child_idx in node['children']:
                if child_idx < len(bone_info['nodes']):
                    bone_info['nodes'][child_idx]['parent'] = node['index']
        
        # 提取人形骨骼映射
        if 'extensions' in json_data and 'VRM' in json_data['extensions']:
            vrm_ext = json_data['extensions']['VRM']
            if 'humanoid' in vrm_ext and 'humanBones' in vrm_ext['humanoid']:
                for human_bone in vrm_ext['humanoid']['humanBones']:
                    bone_name = human_bone.get('bone', '')
                    node_idx = human_bone.get('node', -1)
                    if node_idx >= 0 and node_idx < len(bone_info['nodes']):
                        bone_info['humanoid_bones'][bone_name] = {
                            'node_index': node_idx,
                            'node_name': bone_info['nodes'][node_idx]['name']
                        }
        
        return bone_info
    
    def compare_bone_structures(self, vroid_info, univrm_info):
        """比较两个VRM的骨骼结构"""
        comparison = {
            'vroid_exporter': vroid_info['exporter'],
            'univrm_exporter': univrm_info['exporter'],
            'vroid_node_count': len(vroid_info['nodes']),
            'univrm_node_count': len(univrm_info['nodes']),
            'common_humanoid_bones': [],
            'vroid_only_bones': [],
            'univrm_only_bones': [],
            'bone_name_mapping': {},
            'node_name_differences': []
        }
        
        # 比较人形骨骼
        vroid_bones = set(vroid_info['humanoid_bones'].keys())
        univrm_bones = set(univrm_info['humanoid_bones'].keys())
        
        comparison['common_humanoid_bones'] = list(vroid_bones & univrm_bones)
        comparison['vroid_only_bones'] = list(vroid_bones - univrm_bones)
        comparison['univrm_only_bones'] = list(univrm_bones - vroid_bones)
        
        # 创建骨骼名称映射
        for bone_name in comparison['common_humanoid_bones']:
            vroid_node_name = vroid_info['humanoid_bones'][bone_name]['node_name']
            univrm_node_name = univrm_info['humanoid_bones'][bone_name]['node_name']
            comparison['bone_name_mapping'][bone_name] = {
                'vroid_node': vroid_node_name,
                'univrm_node': univrm_node_name,
                'same_name': vroid_node_name == univrm_node_name
            }
            
            if vroid_node_name != univrm_node_name:
                comparison['node_name_differences'].append({
                    'humanoid_bone': bone_name,
                    'vroid_node': vroid_node_name,
                    'univrm_node': univrm_node_name
                })
        
        return comparison
    
    def save_bone_list(self, bone_info, filename):
        """保存骨骼列表到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"VRM导出器: {bone_info['exporter']}\n")
            f.write(f"节点总数: {len(bone_info['nodes'])}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("所有节点列表:\n")
            f.write("-" * 30 + "\n")
            for node in bone_info['nodes']:
                parent_name = "Root" if node['parent'] == -1 else bone_info['nodes'][node['parent']]['name']
                f.write(f"[{node['index']:03d}] {node['name']} (父节点: {parent_name})\n")
                f.write(f"      位置: {node['translation']}\n")
                if node['children']:
                    child_names = [bone_info['nodes'][child]['name'] for child in node['children']]
                    f.write(f"      子节点: {', '.join(child_names)}\n")
                f.write("\n")
            
            f.write("\n人形骨骼映射:\n")
            f.write("-" * 30 + "\n")
            for bone_name, bone_data in bone_info['humanoid_bones'].items():
                f.write(f"{bone_name} -> {bone_data['node_name']} (节点{bone_data['node_index']})\n")
    
    def save_comparison(self, comparison, filename):
        """保存比较结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("VRM骨骼结构对比分析\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"VRoid导出器版本: {comparison['vroid_exporter']}\n")
            f.write(f"UniVRM导出器版本: {comparison['univrm_exporter']}\n")
            f.write(f"VRoid节点数量: {comparison['vroid_node_count']}\n")
            f.write(f"UniVRM节点数量: {comparison['univrm_node_count']}\n\n")
            
            f.write(f"共同人形骨骼数量: {len(comparison['common_humanoid_bones'])}\n")
            f.write(f"VRoid独有骨骼数量: {len(comparison['vroid_only_bones'])}\n")
            f.write(f"UniVRM独有骨骼数量: {len(comparison['univrm_only_bones'])}\n\n")
            
            if comparison['vroid_only_bones']:
                f.write("VRoid独有的人形骨骼:\n")
                f.write("-" * 30 + "\n")
                for bone in comparison['vroid_only_bones']:
                    f.write(f"- {bone}\n")
                f.write("\n")
            
            if comparison['univrm_only_bones']:
                f.write("UniVRM独有的人形骨骼:\n")
                f.write("-" * 30 + "\n")
                for bone in comparison['univrm_only_bones']:
                    f.write(f"- {bone}\n")
                f.write("\n")
            
            f.write("骨骼节点名称差异:\n")
            f.write("-" * 30 + "\n")
            if comparison['node_name_differences']:
                for diff in comparison['node_name_differences']:
                    f.write(f"人形骨骼: {diff['humanoid_bone']}\n")
                    f.write(f"  VRoid节点名: {diff['vroid_node']}\n")
                    f.write(f"  UniVRM节点名: {diff['univrm_node']}\n\n")
            else:
                f.write("所有共同骨骼的节点名称都相同\n\n")
            
            f.write("完整骨骼名称映射:\n")
            f.write("-" * 30 + "\n")
            for bone_name, mapping in comparison['bone_name_mapping'].items():
                status = "✓" if mapping['same_name'] else "✗"
                f.write(f"{status} {bone_name}:\n")
                f.write(f"    VRoid: {mapping['vroid_node']}\n")
                f.write(f"    UniVRM: {mapping['univrm_node']}\n\n")

def main():
    # VRM文件路径
    vroid_vrm_path = r"I:\AIV\豪德寺美弥子\豪德寺美弥子 Bubbles.vrm"
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    analyzer = VRMAnalyzer()
    
    print("正在分析VRoid导出的VRM文件...")
    vroid_json = analyzer.load_vrm_json(vroid_vrm_path)
    vroid_info = analyzer.extract_bone_info(vroid_json)
    
    print("正在分析UniVRM导出的VRM文件...")
    univrm_json = analyzer.load_vrm_json(univrm_vrm_path)
    univrm_info = analyzer.extract_bone_info(univrm_json)
    
    print("正在比较骨骼结构...")
    comparison = analyzer.compare_bone_structures(vroid_info, univrm_info)
    
    # 保存结果
    print("保存分析结果...")
    analyzer.save_bone_list(vroid_info, "vroid_bones.txt")
    analyzer.save_bone_list(univrm_info, "univrm_bones.txt")
    analyzer.save_comparison(comparison, "vrm_bone_comparison.txt")
    
    print("分析完成！生成的文件:")
    print("- vroid_bones.txt: VRoid导出的VRM骨骼列表")
    print("- univrm_bones.txt: UniVRM导出的VRM骨骼列表")
    print("- vrm_bone_comparison.txt: 骨骼结构对比分析")

if __name__ == "__main__":
    main()
