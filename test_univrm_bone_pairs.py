#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, 'src')

try:
    from service.VroidExportService import VroidExportService, detect_vrm_exporter_type, get_bone_pairs, VROID_BONE_PAIRS, UNIVRM_BONE_PAIRS
    from mmd.VroidReader import VroidReader
    from module.MOptions import MExportOptions
    from utils.MLogger import MLogger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

def test_univrm_bone_pairs():
    """测试UniVRM BONE_PAIRS系统"""
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'log/univrm_bone_pairs_test_{timestamp}.log'
    
    # 确保log目录存在
    os.makedirs('log', exist_ok=True)
    
    MLogger.initialize(level=10, is_file=True, target_lang="zh_CN")
    logger = MLogger(__name__)
    logger.outout_datetime = timestamp
    
    print(f"=== UniVRM BONE_PAIRS系统测试 ===")
    print(f"日志文件: {log_filename}")
    
    # 测试VRM文件路径
    univrm_vrm_path = r"F:\BaiduNetdiskDownload\Kuronyam-weiyi.vrm"
    
    if not os.path.exists(univrm_vrm_path):
        print(f"错误：找不到测试文件 {univrm_vrm_path}")
        return False
    
    try:
        print("1. 读取UniVRM导出的VRM文件...")
        vroid_reader = VroidReader(univrm_vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model:
            print("错误：无法读取VRM文件")
            return False
        
        print(f"   VRM文件读取成功，包含 {len(vrm_model.json_data.get('nodes', []))} 个节点")
        
        # 测试导出器类型检测
        print("2. 检测VRM导出器类型...")
        vrm_type = detect_vrm_exporter_type(vrm_model)
        print(f"   检测到的VRM类型: {vrm_type}")
        
        # 测试BONE_PAIRS选择
        print("3. 选择对应的BONE_PAIRS...")
        bone_pairs = get_bone_pairs(vrm_type)
        print(f"   选择的BONE_PAIRS类型: {'UniVRM' if bone_pairs is UNIVRM_BONE_PAIRS else 'VRoid'}")
        print(f"   BONE_PAIRS包含 {len(bone_pairs)} 个骨骼定义")
        
        # 显示一些关键骨骼的差异
        print("4. 关键骨骼命名对比:")
        key_bones = ["Hips", "Spine", "Chest", "UpperChest", "Neck", "Head"]
        for bone_name in key_bones:
            if bone_name in VROID_BONE_PAIRS:
                vroid_name = VROID_BONE_PAIRS[bone_name]["name"]
                print(f"   VRoid格式: {bone_name} -> {vroid_name}")
            if bone_name in UNIVRM_BONE_PAIRS:
                univrm_name = UNIVRM_BONE_PAIRS[bone_name]["name"]
                print(f"   UniVRM格式: {bone_name} -> {univrm_name}")
        
        # 测试转换过程
        print("5. 测试转换过程...")
        options = MExportOptions(
            version_name="test_version",
            logging_level=10,
            max_workers=1,
            vrm_model=vrm_model,
            physics_flg=None,
            output_path="test_univrm_bone_pairs_output.pmx",
            param_options={},
            monitor=None,
            is_file=True,
            outout_datetime=timestamp
        )
        
        export_service = VroidExportService(options)
        
        # 只测试骨骼转换部分
        print("   开始骨骼转换测试...")
        from mmd.PmxData import PmxModel
        test_model = PmxModel()
        test_model.json_data = vrm_model.json_data
        
        bone_dict, node_dict = export_service.convert_bone(test_model)
        
        if bone_dict is not None and node_dict is not None:
            print(f"   骨骼转换成功！")
            print(f"   创建的骨骼数量: {len(test_model.bones)}")
            print(f"   处理的节点数量: {len(node_dict)}")

            # 显示一些创建的骨骼
            print("   创建的主要骨骼:")
            main_bones = ["全ての親", "センター", "グルーブ", "腰", "下半身", "上半身", "首", "頭"]
            for bone_name in main_bones:
                if bone_name in test_model.bones:
                    bone = test_model.bones[bone_name]
                    print(f"     {bone_name} (英文名: {bone.english_name})")
        else:
            print("   骨骼转换失败")
            return False
        
        print("6. 测试完成！")
        print(f"   详细日志请查看: {log_filename}")
        return True
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_univrm_bone_pairs()
    if success:
        print("\n✅ UniVRM BONE_PAIRS系统测试通过！")
    else:
        print("\n❌ UniVRM BONE_PAIRS系统测试失败！")
        sys.exit(1)
