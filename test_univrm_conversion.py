# -*- coding: utf-8 -*-
"""
测试UniVRM导出的VRM文件转换
"""

import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from mmd.VroidReader import VroidReader
from service.VroidExportService import VroidExportService
from mmd.PmxData import PmxModel
from module.MOptions import MOptionsDataSet

def test_univrm_conversion():
    """测试UniVRM导出的VRM文件转换"""
    
    # UniVRM导出的VRM文件路径
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    if not os.path.exists(univrm_vrm_path):
        print(f"错误：找不到VRM文件 {univrm_vrm_path}")
        return False
    
    try:
        print("开始测试UniVRM导出的VRM文件转换...")
        
        # 创建选项数据集
        options = MOptionsDataSet()
        
        # 读取VRM文件
        print("正在读取VRM文件...")
        vroid_reader = VroidReader(univrm_vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model:
            print("错误：无法读取VRM文件")
            return False
        
        print(f"VRM文件读取成功，包含 {len(vrm_model.json_data.get('nodes', []))} 个节点")
        
        # 设置选项
        options.vrm_model = vrm_model
        options.output_path = "test_univrm_output.pmx"
        options.version_name = "test_version"  # 添加缺失的版本名称
        
        # 创建导出服务
        print("正在创建PMX模型...")
        export_service = VroidExportService(options)
        result = export_service.create_model()

        if not result:
            print("错误：无法创建PMX模型")
            return False

        # 检查返回值类型
        if isinstance(result, tuple):
            pmx_model = result[0]  # 假设第一个元素是PMX模型
            print(f"返回值是元组，长度: {len(result)}")
        else:
            pmx_model = result

        print(f"PMX模型创建成功！")

        # 检查PMX模型的属性
        print(f"PMX模型属性: {dir(pmx_model)}")

        # 尝试获取各种属性
        try:
            print(f"- 顶点数量: {len(pmx_model.vertices)}")
        except:
            print("- 顶点数量: 无法获取")

        try:
            print(f"- 面数量: {len(pmx_model.indices) // 3}")  # 通常面数 = 索引数 / 3
        except:
            try:
                print(f"- 面数量: {len(pmx_model.faces)}")
            except:
                print("- 面数量: 无法获取")

        try:
            print(f"- 骨骼数量: {len(pmx_model.bones)}")
        except:
            print("- 骨骼数量: 无法获取")

        try:
            print(f"- 材质数量: {len(pmx_model.materials)}")
        except:
            print("- 材质数量: 无法获取")
        
        # 检查关键骨骼是否存在
        key_bones = ["全ての親", "センター", "上半身", "下半身", "頭", "首", "左腕", "右腕", "左足", "右足"]
        found_bones = []
        missing_bones = []
        
        bone_names = [bone.name for bone in pmx_model.bones]
        for key_bone in key_bones:
            if key_bone in bone_names:
                found_bones.append(key_bone)
            else:
                missing_bones.append(key_bone)
        
        print(f"\n关键骨骼检查:")
        print(f"- 找到的骨骼: {found_bones}")
        if missing_bones:
            print(f"- 缺失的骨骼: {missing_bones}")
        
        # 保存PMX文件进行验证
        print(f"\n正在保存PMX文件到: {options.output_path}")
        pmx_model.save(options.output_path)
        
        if os.path.exists(options.output_path):
            file_size = os.path.getsize(options.output_path)
            print(f"PMX文件保存成功，文件大小: {file_size} 字节")
            return True
        else:
            print("错误：PMX文件保存失败")
            return False
            
    except Exception as e:
        print(f"转换过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_bone_mapping():
    """测试骨骼名称映射功能"""
    print("\n测试骨骼名称映射功能...")
    
    # 导入映射函数
    from service.VroidExportService import get_bone_name_candidates, BONE_NAME_MAPPING
    
    # 测试一些关键骨骼的映射
    test_bones = [
        "J_Bip_C_Hips",
        "J_Bip_C_Head", 
        "J_Bip_L_Hand",
        "J_Bip_R_Foot",
        "Hips",
        "Head",
        "hand_L",
        "foot_R"
    ]
    
    print("骨骼名称映射测试:")
    for bone_name in test_bones:
        candidates = get_bone_name_candidates(bone_name)
        print(f"  {bone_name} -> {candidates}")
    
    print(f"\n映射表包含 {len(BONE_NAME_MAPPING)} 个映射关系")

if __name__ == "__main__":
    print("UniVRM兼容性测试")
    print("=" * 50)
    
    # 测试骨骼映射
    test_bone_mapping()
    
    # 测试转换
    success = test_univrm_conversion()
    
    if success:
        print("\n✓ 测试成功！UniVRM导出的VRM文件转换正常")
    else:
        print("\n✗ 测试失败！需要进一步调试")
