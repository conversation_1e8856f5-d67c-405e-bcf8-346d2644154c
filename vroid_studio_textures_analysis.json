{"textures_count": 15, "images_count": 15, "textures": [{"index": 0, "source": 0, "sampler": 0}, {"index": 1, "source": 1, "sampler": 0}, {"index": 2, "source": 2, "sampler": 1}, {"index": 3, "source": 3, "sampler": 1}, {"index": 4, "source": 4, "sampler": 1}, {"index": 5, "source": 5, "sampler": 1}, {"index": 6, "source": 6, "sampler": 1}, {"index": 7, "source": 7, "sampler": 1}, {"index": 8, "source": 8, "sampler": 1}, {"index": 9, "source": 9, "sampler": 1}, {"index": 10, "source": 10, "sampler": 1}, {"index": 11, "source": 11, "sampler": 1}, {"index": 12, "source": 12, "sampler": 1}, {"index": 13, "source": 13, "sampler": 1}, {"index": 14, "source": 14, "sampler": 1}], "images": [{"index": 0, "name": "file5", "uri": null, "buffer_view": 0, "mime_type": "image/png"}, {"index": 1, "name": "file2", "uri": null, "buffer_view": 1, "mime_type": "image/png"}, {"index": 2, "name": "texture_0", "uri": null, "buffer_view": 2, "mime_type": "image/png"}, {"index": 3, "name": "texture_7", "uri": null, "buffer_view": 3, "mime_type": "image/png"}, {"index": 4, "name": "texture_2", "uri": null, "buffer_view": 4, "mime_type": "image/png"}, {"index": 5, "name": "texture_1", "uri": null, "buffer_view": 5, "mime_type": "image/png"}, {"index": 6, "name": "Shader_NoneBlack", "uri": null, "buffer_view": 6, "mime_type": "image/png"}, {"index": 7, "name": "texture_9", "uri": null, "buffer_view": 7, "mime_type": "image/png"}, {"index": 8, "name": "Mat<PERSON><PERSON>ar<PERSON>", "uri": null, "buffer_view": 8, "mime_type": "image/png"}, {"index": 9, "name": "texture_3", "uri": null, "buffer_view": 9, "mime_type": "image/png"}, {"index": 10, "name": "texture_11", "uri": null, "buffer_view": 10, "mime_type": "image/png"}, {"index": 11, "name": "texture_5", "uri": null, "buffer_view": 11, "mime_type": "image/png"}, {"index": 12, "name": "texture_4", "uri": null, "buffer_view": 12, "mime_type": "image/png"}, {"index": 13, "name": "texture_12", "uri": null, "buffer_view": 13, "mime_type": "image/png"}, {"index": 14, "name": "<PERSON><PERSON><PERSON><PERSON>", "uri": null, "buffer_view": 184, "mime_type": "image/png"}]}