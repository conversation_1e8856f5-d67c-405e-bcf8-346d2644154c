# -*- coding: utf-8 -*-
"""
最终的UniVRM兼容性测试
"""

import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from mmd.VroidReader import VroidReader
from service.VroidExportService import VroidExportService, get_bone_name_candidates, BONE_NAME_MAPPING
from module.MOptions import MOptionsDataSet

def test_bone_mapping():
    """测试骨骼名称映射功能"""
    print("测试骨骼名称映射功能...")
    
    # 测试一些关键的映射
    test_cases = [
        "J_Bip_C_Hips", "J_Bip_C_Head", "J_Bip_L_Hand", "J_Bip_R_Foot",
        "Hips", "Head", "hand_L", "foot_R"
    ]
    
    print("骨骼名称映射测试:")
    for bone_name in test_cases:
        candidates = get_bone_name_candidates(bone_name)
        print(f"  {bone_name} -> {candidates}")
    
    print(f"\n映射表包含 {len(BONE_NAME_MAPPING)} 个映射关系")

def test_univrm_conversion():
    """测试UniVRM导出的VRM文件转换"""
    
    # UniVRM导出的VRM文件路径
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    if not os.path.exists(univrm_vrm_path):
        print(f"错误：找不到VRM文件 {univrm_vrm_path}")
        return False
    
    try:
        print("开始测试UniVRM导出的VRM文件转换...")
        
        # 读取VRM文件
        print("正在读取VRM文件...")
        vroid_reader = VroidReader(univrm_vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model:
            print("错误：无法读取VRM文件")
            return False
        
        print(f"VRM文件读取成功，包含 {len(vrm_model.json_data.get('nodes', []))} 个节点")
        
        # 设置选项
        options = MOptionsDataSet()
        options.vrm_model = vrm_model
        options.output_path = "final_test_univrm_output.pmx"
        options.version_name = "final_test_version"
        
        # 创建导出服务并执行完整转换
        print("正在执行完整转换流程...")
        export_service = VroidExportService(options)
        
        # 执行完整的vroid2pmx转换流程
        pmx_model = export_service.vroid2pmx()
        
        if not pmx_model:
            print("错误：转换失败")
            return False
        
        print(f"✓ 转换成功！")
        print(f"- 顶点数量: {len(pmx_model.vertices)}")
        print(f"- 骨骼数量: {len(pmx_model.bones)}")
        print(f"- 材质数量: {len(pmx_model.materials)}")
        
        # 检查关键骨骼
        key_bones = ["全ての親", "センター", "上半身", "下半身", "頭", "首", "左腕", "右腕", "左足", "右足"]

        # pmx_model.bones 是一个字典，键是骨骼名称
        if isinstance(pmx_model.bones, dict):
            bone_names = list(pmx_model.bones.keys())
        else:
            bone_names = [bone.name for bone in pmx_model.bones]

        found_bones = [bone for bone in key_bones if bone in bone_names]
        
        print(f"\n关键骨骼检查:")
        print(f"- 找到的骨骼: {found_bones}")
        missing_bones = [bone for bone in key_bones if bone not in found_bones]
        if missing_bones:
            print(f"- 缺失的骨骼: {missing_bones}")
        else:
            print("- ✓ 所有关键骨骼都找到了")
        
        # 检查PMX文件是否已保存
        if os.path.exists(options.output_path):
            print(f"\n✓ PMX文件已保存到: {options.output_path}")
            file_size = os.path.getsize(options.output_path)
            print(f"  文件大小: {file_size:,} 字节")
        else:
            print(f"\n⚠️  PMX文件未找到: {options.output_path}")
        
        print("\n🎉 测试成功！UniVRM兼容性验证通过")
        print("=" * 50)
        print("总结:")
        print(f"✓ 成功读取UniVRM导出的VRM文件")
        print(f"✓ 骨骼名称映射工作正常")
        print(f"✓ 完整转换流程执行成功")
        print(f"✓ 生成了有效的PMX模型")
        print(f"✓ 找到了 {len(found_bones)}/{len(key_bones)} 个关键骨骼")
        
        return True
        
    except Exception as e:
        print(f"转换过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n✗ 测试失败！")
        return False

def main():
    """主测试函数"""
    print("UniVRM兼容性最终测试")
    print("=" * 50)
    
    # 测试骨骼映射
    test_bone_mapping()
    
    print()
    
    # 测试转换
    success = test_univrm_conversion()
    
    if success:
        print("\n🎉 所有测试通过！")
        print("vroid2pmx现在支持UniVRM导出的VRM文件！")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
