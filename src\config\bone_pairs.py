"""
VRM骨骼映射配置文件
包含VRoid Studio和UniVRM两种不同的骨骼命名规范的BONE_PAIRS定义
"""

from module.MMath import MVector3D

# VRoid Studio专用的BONE_PAIRS
VROID_BONE_PAIRS = {
    "Root": {
        "name": "全ての親",
        "parent": None,
        "tail": "Center",
        "display": None,
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "Center": {
        "name": "センター",
        "parent": "Root",
        "tail": None,
        "display": "センター",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "Groove": {
        "name": "グルーブ",
        "parent": "Center",
        "tail": None,
        "display": "センター",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Bip_C_Hips": {
        "name": "腰",
        "parent": "Groove",
        "tail": None,
        "display": "体幹",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Bip_C_Spine": {
        "name": "下半身",
        "parent": "J_Bip_C_Hips",
        "tail": None,
        "display": "体幹",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_C_Spine2": {
        "name": "上半身",
        "parent": "J_Bip_C_Hips",
        "tail": "J_Bip_C_Chest",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_C_Chest": {
        "name": "上半身2",
        "parent": "J_Bip_C_Spine2",
        "tail": "J_Bip_C_UpperChest",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_C_UpperChest": {
        "name": "上半身3",
        "parent": "J_Bip_C_Chest",
        "tail": "J_Bip_C_Neck",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_C_Neck": {
        "name": "首",
        "parent": "J_Bip_C_UpperChest",
        "tail": "J_Bip_C_Head",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_C_Head": {
        "name": "頭",
        "parent": "J_Bip_C_Neck",
        "tail": None,
        "display": "体幹",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_FaceEye": {
        "name": "両目",
        "parent": "J_Bip_C_Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_L_FaceEye": {
        "name": "左目",
        "parent": "J_Bip_C_Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_R_FaceEye": {
        "name": "右目",
        "parent": "J_Bip_C_Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_FaceEyeHighlight": {
        "name": "両目光",
        "parent": "J_Adj_FaceEye",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Adj_L_FaceEyeHighlight": {
        "name": "左目光",
        "parent": "J_Bip_C_Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Adj_R_FaceEyeHighlight": {
        "name": "右目光",
        "parent": "J_Bip_C_Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Adj_FaceTongue1": {
        "name": "舌1",
        "parent": "J_Bip_C_Head",
        "tail": "J_Adj_FaceTongue2",
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Adj_FaceTongue2": {
        "name": "舌2",
        "parent": "J_Adj_FaceTongue1",
        "tail": "J_Adj_FaceTongue3",
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Adj_FaceTongue3": {
        "name": "舌3",
        "parent": "J_Adj_FaceTongue2",
        "tail": "J_Adj_FaceTongue4",
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Adj_FaceTongue4": {
        "name": "舌4",
        "parent": "J_Adj_FaceTongue3",
        "tail": None,
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Sec_L_Bust1": {
        "name": "左胸",
        "parent": "J_Bip_C_UpperChest",
        "tail": "J_Sec_L_Bust2",
        "display": "胸",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Sec_L_Bust2": {
        "name": "左胸先",
        "parent": "J_Sec_L_Bust1",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Sec_R_Bust1": {
        "name": "右胸",
        "parent": "J_Bip_C_UpperChest",
        "tail": "J_Sec_R_Bust2",
        "display": "胸",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Sec_R_Bust2": {
        "name": "右胸先",
        "parent": "J_Sec_R_Bust1",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "shoulderP_L": {
        "name": "左肩P",
        "parent": "J_Bip_C_UpperChest",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_L_Shoulder": {
        "name": "左肩",
        "parent": "shoulderP_L",
        "tail": "J_Bip_L_UpperArm",
        "display": "左手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "shoulderC_L": {
        "name": "左肩C",
        "parent": "J_Bip_L_Shoulder",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_L_UpperArm": {
        "name": "左腕",
        "parent": "shoulderC_L",
        "tail": "J_Bip_L_LowerArm",
        "display": "左手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "arm_twist_L": {
        "name": "左腕捩",
        "parent": "J_Bip_L_UpperArm",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800 | 0x0800,
    },
    "arm_twist_1L": {
        "name": "左腕捩1",
        "parent": "J_Bip_L_UpperArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_2L": {
        "name": "左腕捩2",
        "parent": "J_Bip_L_UpperArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_3L": {
        "name": "左腕捩3",
        "parent": "J_Bip_L_UpperArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_L_LowerArm": {
        "name": "左ひじ",
        "parent": "arm_twist_L",
        "tail": "J_Bip_L_Hand",
        "display": "左手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "wrist_twist_L": {
        "name": "左手捩",
        "parent": "J_Bip_L_LowerArm",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800,
    },
    "wrist_twist_1L": {
        "name": "左手捩1",
        "parent": "J_Bip_L_LowerArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_2L": {
        "name": "左手捩2",
        "parent": "J_Bip_L_LowerArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_3L": {
        "name": "左手捩3",
        "parent": "J_Bip_L_LowerArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_L_Hand": {
        "name": "左手首",
        "parent": "wrist_twist_L",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Thumb1": {
        "name": "左親指０",
        "parent": "J_Bip_L_Hand",
        "tail": "J_Bip_L_Thumb2",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Thumb2": {
        "name": "左親指１",
        "parent": "J_Bip_L_Thumb1",
        "tail": "J_Bip_L_Thumb3",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Thumb3": {
        "name": "左親指２",
        "parent": "J_Bip_L_Thumb2",
        "tail": "J_Bip_L_Thumb3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Thumb3_end": {
        "name": "左親指先",
        "parent": "J_Bip_L_Thumb3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_L_Index1": {
        "name": "左人指１",
        "parent": "J_Bip_L_Hand",
        "tail": "J_Bip_L_Index2",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Index2": {
        "name": "左人指２",
        "parent": "J_Bip_L_Index1",
        "tail": "J_Bip_L_Index3",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Index3": {
        "name": "左人指３",
        "parent": "J_Bip_L_Index2",
        "tail": "J_Bip_L_Index3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Index3_end": {
        "name": "左人指先",
        "parent": "J_Bip_L_Index3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_L_Middle1": {
        "name": "左中指１",
        "parent": "J_Bip_L_Hand",
        "tail": "J_Bip_L_Middle2",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Middle2": {
        "name": "左中指２",
        "parent": "J_Bip_L_Middle1",
        "tail": "J_Bip_L_Middle3",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Middle3": {
        "name": "左中指３",
        "parent": "J_Bip_L_Middle2",
        "tail": "J_Bip_L_Middle3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Middle3_end": {
        "name": "左中指先",
        "parent": "J_Bip_L_Middle3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_L_Ring1": {
        "name": "左薬指１",
        "parent": "J_Bip_L_Hand",
        "tail": "J_Bip_L_Ring2",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Ring2": {
        "name": "左薬指２",
        "parent": "J_Bip_L_Ring1",
        "tail": "J_Bip_L_Ring3",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Ring3": {
        "name": "左薬指３",
        "parent": "J_Bip_L_Ring2",
        "tail": "J_Bip_L_Ring3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Ring3_end": {
        "name": "左薬指先",
        "parent": "J_Bip_L_Ring3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_L_Little1": {
        "name": "左小指１",
        "parent": "J_Bip_L_Hand",
        "tail": "J_Bip_L_Little2",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Little2": {
        "name": "左小指２",
        "parent": "J_Bip_L_Little1",
        "tail": "J_Bip_L_Little3",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Little3": {
        "name": "左小指３",
        "parent": "J_Bip_L_Little2",
        "tail": "J_Bip_L_Little3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Little3_end": {
        "name": "左小指先",
        "parent": "J_Bip_L_Little3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "shoulderP_R": {
        "name": "右肩P",
        "parent": "J_Bip_C_UpperChest",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_R_Shoulder": {
        "name": "右肩",
        "parent": "shoulderP_R",
        "tail": "J_Bip_R_UpperArm",
        "display": "右手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "shoulderC_R": {
        "name": "右肩C",
        "parent": "J_Bip_R_Shoulder",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_R_UpperArm": {
        "name": "右腕",
        "parent": "shoulderC_R",
        "tail": "J_Bip_R_LowerArm",
        "display": "右手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "arm_twist_R": {
        "name": "右腕捩",
        "parent": "J_Bip_R_UpperArm",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800,
    },
    "arm_twist_1R": {
        "name": "右腕捩1",
        "parent": "J_Bip_R_UpperArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_2R": {
        "name": "右腕捩2",
        "parent": "J_Bip_R_UpperArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_3R": {
        "name": "右腕捩3",
        "parent": "J_Bip_R_UpperArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_R_LowerArm": {
        "name": "右ひじ",
        "parent": "arm_twist_R",
        "tail": "J_Bip_R_Hand",
        "display": "右手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "wrist_twist_R": {
        "name": "右手捩",
        "parent": "J_Bip_R_LowerArm",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800,
    },
    "wrist_twist_1R": {
        "name": "右手捩1",
        "parent": "J_Bip_R_LowerArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_2R": {
        "name": "右手捩2",
        "parent": "J_Bip_R_LowerArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_3R": {
        "name": "右手捩3",
        "parent": "J_Bip_R_LowerArm",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_R_Hand": {
        "name": "右手首",
        "parent": "wrist_twist_R",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Thumb1": {
        "name": "右親指０",
        "parent": "J_Bip_R_Hand",
        "tail": "J_Bip_R_Thumb2",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Thumb2": {
        "name": "右親指１",
        "parent": "J_Bip_R_Thumb1",
        "tail": "J_Bip_R_Thumb3",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Thumb3": {
        "name": "右親指２",
        "parent": "J_Bip_R_Thumb2",
        "tail": "J_Bip_R_Thumb3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Thumb3_end": {
        "name": "右親指先",
        "parent": "J_Bip_R_Thumb3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_R_Index1": {
        "name": "右人指１",
        "parent": "J_Bip_R_Hand",
        "tail": "J_Bip_R_Index2",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Index2": {
        "name": "右人指２",
        "parent": "J_Bip_R_Index1",
        "tail": "J_Bip_R_Index3",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Index3": {
        "name": "右人指３",
        "parent": "J_Bip_R_Index2",
        "tail": "J_Bip_R_Index3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Index3_end": {
        "name": "右人指先",
        "parent": "J_Bip_R_Index3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_R_Middle1": {
        "name": "右中指１",
        "parent": "J_Bip_R_Hand",
        "tail": "J_Bip_R_Middle2",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Middle2": {
        "name": "右中指２",
        "parent": "J_Bip_R_Middle1",
        "tail": "J_Bip_R_Middle3",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Middle3": {
        "name": "右中指３",
        "parent": "J_Bip_R_Middle2",
        "tail": "J_Bip_R_Middle3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Middle3_end": {
        "name": "右中指先",
        "parent": "J_Bip_R_Middle3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_R_Ring1": {
        "name": "右薬指１",
        "parent": "J_Bip_R_Hand",
        "tail": "J_Bip_R_Ring2",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Ring2": {
        "name": "右薬指２",
        "parent": "J_Bip_R_Ring1",
        "tail": "J_Bip_R_Ring3",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Ring3": {
        "name": "右薬指３",
        "parent": "J_Bip_R_Ring2",
        "tail": "J_Bip_R_Ring3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Ring3_end": {
        "name": "右薬指先",
        "parent": "J_Bip_R_Ring3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Bip_R_Little1": {
        "name": "右小指１",
        "parent": "J_Bip_R_Hand",
        "tail": "J_Bip_R_Little2",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Little2": {
        "name": "右小指２",
        "parent": "J_Bip_R_Little1",
        "tail": "J_Bip_R_Little3",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Little3": {
        "name": "右小指３",
        "parent": "J_Bip_R_Little2",
        "tail": "J_Bip_R_Little3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Little3_end": {
        "name": "右小指先",
        "parent": "J_Bip_R_Little3",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "waistCancel_L": {
        "name": "腰キャンセル左",
        "parent": "J_Bip_C_Spine",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_L_UpperLeg": {
        "name": "左足",
        "parent": "waistCancel_L",
        "tail": "J_Bip_L_LowerLeg",
        "display": "左足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_L_LowerLeg": {
        "name": "左ひざ",
        "parent": "J_Bip_L_UpperLeg",
        "tail": "J_Bip_L_Foot",
        "display": "左足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_L_Foot": {
        "name": "左足首",
        "parent": "J_Bip_L_LowerLeg",
        "tail": "J_Bip_L_ToeBase",
        "display": "左足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_L_ToeBase": {
        "name": "左つま先",
        "parent": "J_Bip_L_Foot",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "leg_IK_Parent_L": {
        "name": "左足IK親",
        "parent": "Root",
        "tail": "leg_IK_L",
        "display": "左足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "leg_IK_L": {
        "name": "左足ＩＫ",
        "parent": "leg_IK_Parent_L",
        "tail": MVector3D(0, 0, 1),
        "display": "左足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "toe_IK_L": {
        "name": "左つま先ＩＫ",
        "parent": "leg_IK_L",
        "tail": MVector3D(0, -1, 0),
        "display": "左足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "waistCancel_R": {
        "name": "腰キャンセル右",
        "parent": "J_Bip_C_Spine",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "J_Bip_R_UpperLeg": {
        "name": "右足",
        "parent": "waistCancel_R",
        "tail": "J_Bip_R_LowerLeg",
        "display": "右足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_R_LowerLeg": {
        "name": "右ひざ",
        "parent": "J_Bip_R_UpperLeg",
        "tail": "J_Bip_R_Foot",
        "display": "右足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_R_Foot": {
        "name": "右足首",
        "parent": "J_Bip_R_LowerLeg",
        "tail": "J_Bip_R_ToeBase",
        "display": "右足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Bip_R_ToeBase": {
        "name": "右つま先",
        "parent": "J_Bip_R_Foot",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "leg_IK_Parent_R": {
        "name": "右足IK親",
        "parent": "Root",
        "tail": "leg_IK_R",
        "display": "右足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "leg_IK_R": {
        "name": "右足ＩＫ",
        "parent": "leg_IK_Parent_R",
        "tail": MVector3D(0, 0, 1),
        "display": "右足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "toe_IK_R": {
        "name": "右つま先ＩＫ",
        "parent": "leg_IK_R",
        "tail": MVector3D(0, -1, 0),
        "display": "右足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "leg_D_L": {
        "name": "左足D",
        "parent": "waistCancel_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "knee_D_L": {
        "name": "左ひざD",
        "parent": "leg_D_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "ankle_D_L": {
        "name": "左足首D",
        "parent": "knee_D_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "toe_EX_L": {
        "name": "左足先EX",
        "parent": "ankle_D_L",
        "tail": MVector3D(0, 0, -1),
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "leg_D_R": {
        "name": "右足D",
        "parent": "waistCancel_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "knee_D_R": {
        "name": "右ひざD",
        "parent": "leg_D_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "ankle_D_R": {
        "name": "右足首D",
        "parent": "knee_D_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "toe_EX_R": {
        "name": "右足先EX",
        "parent": "ankle_D_R",
        "tail": MVector3D(0, 0, -1),
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
}

# UniVRM专用的BONE_PAIRS (使用UniVRM骨骼命名)
UNIVRM_BONE_PAIRS = {
    "Root": {
        "name": "全ての親",
        "parent": None,
        "tail": "Center",
        "display": None,
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "Center": {
        "name": "センター",
        "parent": "Root",
        "tail": None,
        "display": "センター",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "Groove": {
        "name": "グルーブ",
        "parent": "Center",
        "tail": None,
        "display": "センター",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "Hips": {
        "name": "腰",
        "parent": "Groove",
        "tail": None,
        "display": "体幹",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "Spine": {
        "name": "下半身",
        "parent": "Hips",
        "tail": None,
        "display": "体幹",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "Chest": {
        "name": "上半身",
        "parent": "Hips",
        "tail": "UpperChest",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "UpperChest": {
        "name": "上半身2",
        "parent": "Chest",
        "tail": "UpperChest",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "UpperChest": {
        "name": "上半身3",
        "parent": "UpperChest",
        "tail": "Neck",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "Neck": {
        "name": "首",
        "parent": "UpperChest",
        "tail": "Head",
        "display": "体幹",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "Head": {
        "name": "頭",
        "parent": "Neck",
        "tail": None,
        "display": "体幹",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_FaceEye": {
        "name": "両目",
        "parent": "Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_L_FaceEye": {
        "name": "左目",
        "parent": "Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_R_FaceEye": {
        "name": "右目",
        "parent": "Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "J_Adj_FaceEyeHighlight": {
        "name": "両目光",
        "parent": "J_Adj_FaceEye",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Adj_L_FaceEyeHighlight": {
        "name": "左目光",
        "parent": "Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Adj_R_FaceEyeHighlight": {
        "name": "右目光",
        "parent": "Head",
        "tail": None,
        "display": "顔",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "J_Adj_FaceTongue1": {
        "name": "舌1",
        "parent": "Head",
        "tail": "J_Adj_FaceTongue2",
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Adj_FaceTongue2": {
        "name": "舌2",
        "parent": "J_Adj_FaceTongue1",
        "tail": "J_Adj_FaceTongue3",
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Adj_FaceTongue3": {
        "name": "舌3",
        "parent": "J_Adj_FaceTongue2",
        "tail": "J_Adj_FaceTongue4",
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Adj_FaceTongue4": {
        "name": "舌4",
        "parent": "J_Adj_FaceTongue3",
        "tail": None,
        "display": "顔",
        "flag": 0x0001 | 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Sec_L_Bust1": {
        "name": "左胸",
        "parent": "UpperChest",
        "tail": "J_Sec_L_Bust2",
        "display": "胸",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Sec_L_Bust2": {
        "name": "左胸先",
        "parent": "J_Sec_L_Bust1",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "J_Sec_R_Bust1": {
        "name": "右胸",
        "parent": "UpperChest",
        "tail": "J_Sec_R_Bust2",
        "display": "胸",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "J_Sec_R_Bust2": {
        "name": "右胸先",
        "parent": "J_Sec_R_Bust1",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "shoulderP_L": {
        "name": "左肩P",
        "parent": "UpperChest",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "shoulder_L": {
        "name": "左肩",
        "parent": "shoulderP_L",
        "tail": "upperArm_L",
        "display": "左手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "shoulderC_L": {
        "name": "左肩C",
        "parent": "shoulder_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "upperArm_L": {
        "name": "左腕",
        "parent": "shoulderC_L",
        "tail": "lowerArm_L",
        "display": "左手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "arm_twist_L": {
        "name": "左腕捩",
        "parent": "upperArm_L",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800 | 0x0800,
    },
    "arm_twist_1L": {
        "name": "左腕捩1",
        "parent": "upperArm_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_2L": {
        "name": "左腕捩2",
        "parent": "upperArm_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_3L": {
        "name": "左腕捩3",
        "parent": "upperArm_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "lowerArm_L": {
        "name": "左ひじ",
        "parent": "arm_twist_L",
        "tail": "hand_L",
        "display": "左手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "wrist_twist_L": {
        "name": "左手捩",
        "parent": "lowerArm_L",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800,
    },
    "wrist_twist_1L": {
        "name": "左手捩1",
        "parent": "lowerArm_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_2L": {
        "name": "左手捩2",
        "parent": "lowerArm_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_3L": {
        "name": "左手捩3",
        "parent": "lowerArm_L",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "hand_L": {
        "name": "左手首",
        "parent": "wrist_twist_L",
        "tail": None,
        "display": "左手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "ThumbProximal_L": {
        "name": "左親指０",
        "parent": "hand_L",
        "tail": "ThumbIntermediate_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "ThumbIntermediate_L": {
        "name": "左親指１",
        "parent": "ThumbProximal_L",
        "tail": "ThumbDistal_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "ThumbDistal_L": {
        "name": "左親指２",
        "parent": "ThumbIntermediate_L",
        "tail": "J_Bip_L_Thumb3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Thumb3_end": {
        "name": "左親指先",
        "parent": "ThumbDistal_L",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "IndexProximal_L": {
        "name": "左人指１",
        "parent": "hand_L",
        "tail": "IndexIntermediate_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "IndexIntermediate_L": {
        "name": "左人指２",
        "parent": "IndexProximal_L",
        "tail": "IndexDistal_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "IndexDistal_L": {
        "name": "左人指３",
        "parent": "IndexIntermediate_L",
        "tail": "J_Bip_L_Index3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Index3_end": {
        "name": "左人指先",
        "parent": "IndexDistal_L",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "MiddleProximal_L": {
        "name": "左中指１",
        "parent": "hand_L",
        "tail": "MiddleIntermediate_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "MiddleIntermediate_L": {
        "name": "左中指２",
        "parent": "MiddleProximal_L",
        "tail": "MiddleDistal_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "MiddleDistal_L": {
        "name": "左中指３",
        "parent": "MiddleIntermediate_L",
        "tail": "J_Bip_L_Middle3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Middle3_end": {
        "name": "左中指先",
        "parent": "MiddleDistal_L",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "RingProximal_L": {
        "name": "左薬指１",
        "parent": "hand_L",
        "tail": "RingIntermediate_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "RingIntermediate_L": {
        "name": "左薬指２",
        "parent": "RingProximal_L",
        "tail": "RingDistal_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "RingDistal_L": {
        "name": "左薬指３",
        "parent": "RingIntermediate_L",
        "tail": "J_Bip_L_Ring3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Ring3_end": {
        "name": "左薬指先",
        "parent": "RingDistal_L",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "LittleProximal_L": {
        "name": "左小指１",
        "parent": "hand_L",
        "tail": "LittleIntermediate_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "LittleIntermediate_L": {
        "name": "左小指２",
        "parent": "LittleProximal_L",
        "tail": "LittleDistal_L",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "LittleDistal_L": {
        "name": "左小指３",
        "parent": "LittleIntermediate_L",
        "tail": "J_Bip_L_Little3_end",
        "display": "左指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_L_Little3_end": {
        "name": "左小指先",
        "parent": "LittleDistal_L",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "shoulderP_R": {
        "name": "右肩P",
        "parent": "UpperChest",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "shoulder_R": {
        "name": "右肩",
        "parent": "shoulderP_R",
        "tail": "upperArm_R",
        "display": "右手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "shoulderC_R": {
        "name": "右肩C",
        "parent": "shoulder_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "upperArm_R": {
        "name": "右腕",
        "parent": "shoulderC_R",
        "tail": "lowerArm_R",
        "display": "右手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "arm_twist_R": {
        "name": "右腕捩",
        "parent": "upperArm_R",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800,
    },
    "arm_twist_1R": {
        "name": "右腕捩1",
        "parent": "upperArm_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_2R": {
        "name": "右腕捩2",
        "parent": "upperArm_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "arm_twist_3R": {
        "name": "右腕捩3",
        "parent": "upperArm_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "lowerArm_R": {
        "name": "右ひじ",
        "parent": "arm_twist_R",
        "tail": "hand_R",
        "display": "右手",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "wrist_twist_R": {
        "name": "右手捩",
        "parent": "lowerArm_R",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0400 | 0x0800,
    },
    "wrist_twist_1R": {
        "name": "右手捩1",
        "parent": "lowerArm_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_2R": {
        "name": "右手捩2",
        "parent": "lowerArm_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "wrist_twist_3R": {
        "name": "右手捩3",
        "parent": "lowerArm_R",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "hand_R": {
        "name": "右手首",
        "parent": "wrist_twist_R",
        "tail": None,
        "display": "右手",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "ThumbProximal_R": {
        "name": "右親指０",
        "parent": "hand_R",
        "tail": "ThumbIntermediate_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "ThumbIntermediate_R": {
        "name": "右親指１",
        "parent": "ThumbProximal_R",
        "tail": "ThumbDistal_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "ThumbDistal_R": {
        "name": "右親指２",
        "parent": "ThumbIntermediate_R",
        "tail": "J_Bip_R_Thumb3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Thumb3_end": {
        "name": "右親指先",
        "parent": "ThumbDistal_R",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "IndexProximal_R": {
        "name": "右人指１",
        "parent": "hand_R",
        "tail": "IndexIntermediate_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "IndexIntermediate_R": {
        "name": "右人指２",
        "parent": "IndexProximal_R",
        "tail": "IndexDistal_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "IndexDistal_R": {
        "name": "右人指３",
        "parent": "IndexIntermediate_R",
        "tail": "J_Bip_R_Index3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Index3_end": {
        "name": "右人指先",
        "parent": "IndexDistal_R",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "MiddleProximal_R": {
        "name": "右中指１",
        "parent": "hand_R",
        "tail": "MiddleIntermediate_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "MiddleIntermediate_R": {
        "name": "右中指２",
        "parent": "MiddleProximal_R",
        "tail": "MiddleDistal_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "MiddleDistal_R": {
        "name": "右中指３",
        "parent": "MiddleIntermediate_R",
        "tail": "J_Bip_R_Middle3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Middle3_end": {
        "name": "右中指先",
        "parent": "MiddleDistal_R",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "RingProximal_R": {
        "name": "右薬指１",
        "parent": "hand_R",
        "tail": "RingIntermediate_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "RingIntermediate_R": {
        "name": "右薬指２",
        "parent": "RingProximal_R",
        "tail": "RingDistal_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "RingDistal_R": {
        "name": "右薬指３",
        "parent": "RingIntermediate_R",
        "tail": "J_Bip_R_Ring3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Ring3_end": {
        "name": "右薬指先",
        "parent": "RingDistal_R",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "LittleProximal_R": {
        "name": "右小指１",
        "parent": "hand_R",
        "tail": "LittleIntermediate_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "LittleIntermediate_R": {
        "name": "右小指２",
        "parent": "LittleProximal_R",
        "tail": "LittleDistal_R",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "LittleDistal_R": {
        "name": "右小指３",
        "parent": "LittleIntermediate_R",
        "tail": "J_Bip_R_Little3_end",
        "display": "右指",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010 | 0x0800,
    },
    "J_Bip_R_Little3_end": {
        "name": "右小指先",
        "parent": "LittleDistal_R",
        "tail": None,
        "display": None,
        "flag": 0x0002,
    },
    "waistCancel_L": {
        "name": "腰キャンセル左",
        "parent": "Spine",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "upperLeg_L": {
        "name": "左足",
        "parent": "waistCancel_L",
        "tail": "lowerLeg_L",
        "display": "左足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "lowerLeg_L": {
        "name": "左ひざ",
        "parent": "upperLeg_L",
        "tail": "foot_L",
        "display": "左足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "foot_L": {
        "name": "左足首",
        "parent": "lowerLeg_L",
        "tail": "toe_L",
        "display": "左足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "toe_L": {
        "name": "左つま先",
        "parent": "foot_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "leg_IK_Parent_L": {
        "name": "左足IK親",
        "parent": "Root",
        "tail": "leg_IK_L",
        "display": "左足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "leg_IK_L": {
        "name": "左足ＩＫ",
        "parent": "leg_IK_Parent_L",
        "tail": MVector3D(0, 0, 1),
        "display": "左足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "toe_IK_L": {
        "name": "左つま先ＩＫ",
        "parent": "leg_IK_L",
        "tail": MVector3D(0, -1, 0),
        "display": "左足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "waistCancel_R": {
        "name": "腰キャンセル右",
        "parent": "Spine",
        "tail": None,
        "display": None,
        "flag": 0x0002 | 0x0100,
    },
    "upperLeg_R": {
        "name": "右足",
        "parent": "waistCancel_R",
        "tail": "lowerLeg_R",
        "display": "右足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "lowerLeg_R": {
        "name": "右ひざ",
        "parent": "upperLeg_R",
        "tail": "foot_R",
        "display": "右足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "foot_R": {
        "name": "右足首",
        "parent": "lowerLeg_R",
        "tail": "toe_R",
        "display": "右足",
        "flag": 0x0001 | 0x0002 | 0x0008 | 0x0010,
    },
    "toe_R": {
        "name": "右つま先",
        "parent": "foot_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "leg_IK_Parent_R": {
        "name": "右足IK親",
        "parent": "Root",
        "tail": "leg_IK_R",
        "display": "右足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010,
    },
    "leg_IK_R": {
        "name": "右足ＩＫ",
        "parent": "leg_IK_Parent_R",
        "tail": MVector3D(0, 0, 1),
        "display": "右足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "toe_IK_R": {
        "name": "右つま先ＩＫ",
        "parent": "leg_IK_R",
        "tail": MVector3D(0, -1, 0),
        "display": "右足",
        "flag": 0x0002 | 0x0004 | 0x0008 | 0x0010 | 0x0020,
    },
    "leg_D_L": {
        "name": "左足D",
        "parent": "waistCancel_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "knee_D_L": {
        "name": "左ひざD",
        "parent": "leg_D_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "ankle_D_L": {
        "name": "左足首D",
        "parent": "knee_D_L",
        "tail": None,
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "toe_EX_L": {
        "name": "左足先EX",
        "parent": "ankle_D_L",
        "tail": MVector3D(0, 0, -1),
        "display": "左足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
    "leg_D_R": {
        "name": "右足D",
        "parent": "waistCancel_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "knee_D_R": {
        "name": "右ひざD",
        "parent": "leg_D_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "ankle_D_R": {
        "name": "右足首D",
        "parent": "knee_D_R",
        "tail": None,
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010 | 0x0100,
    },
    "toe_EX_R": {
        "name": "右足先EX",
        "parent": "ankle_D_R",
        "tail": MVector3D(0, 0, -1),
        "display": "右足",
        "flag": 0x0002 | 0x0008 | 0x0010,
    },
}

# 根据VRM类型选择对应的BONE_PAIRS
def get_bone_pairs(vrm_type):
    """根据VRM导出器类型返回对应的BONE_PAIRS"""
    if vrm_type == "UniVRM":
        return UNIVRM_BONE_PAIRS
    else:
        return VROID_BONE_PAIRS

# 为了向后兼容，保持原有的BONE_PAIRS变量
BONE_PAIRS = VROID_BONE_PAIRS
