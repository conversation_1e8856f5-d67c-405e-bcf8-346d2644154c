# -*- coding: utf-8 -*-
"""
测试GUI转换功能
"""

import sys
import os
import logging
from pathlib import Path

# 设置日志输出到文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('conversion_test.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_conversion():
    """测试转换功能"""
    try:
        from mmd.VroidReader import VroidReader
        from service.VroidExportService import VroidExportService
        from module.MOptions import MOptionsDataSet
        from utils.MLogger import MLogger
        
        # 初始化日志系统
        MLogger.initialize(level=logging.INFO, is_file=True)
        
        logger.info("开始测试UniVRM转换功能...")
        
        # UniVRM导出的VRM文件路径
        univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
        
        if not os.path.exists(univrm_vrm_path):
            logger.error(f"找不到VRM文件: {univrm_vrm_path}")
            return False
        
        # 读取VRM文件
        logger.info("正在读取VRM文件...")
        vroid_reader = VroidReader(univrm_vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model:
            logger.error("无法读取VRM文件")
            return False
        
        logger.info(f"VRM文件读取成功，包含 {len(vrm_model.json_data.get('nodes', []))} 个节点")
        
        # 设置选项
        options = MOptionsDataSet()
        options.vrm_model = vrm_model
        options.output_path = "test_gui_conversion_output.pmx"
        options.version_name = "gui_test_version"
        
        # 创建导出服务并执行转换
        logger.info("正在执行转换...")
        export_service = VroidExportService(options)
        
        # 执行完整的vroid2pmx转换流程
        pmx_model = export_service.vroid2pmx()
        
        if not pmx_model:
            logger.error("转换失败")
            return False
        
        logger.info("✓ 转换成功！")
        logger.info(f"- 顶点数量: {len(pmx_model.vertices)}")
        logger.info(f"- 骨骼数量: {len(pmx_model.bones)}")
        logger.info(f"- 材质数量: {len(pmx_model.materials)}")
        
        # 检查PMX文件是否已保存
        if os.path.exists(options.output_path):
            logger.info(f"✓ PMX文件已保存到: {options.output_path}")
            file_size = os.path.getsize(options.output_path)
            logger.info(f"  文件大小: {file_size:,} 字节")
        else:
            logger.warning(f"⚠️  PMX文件未找到: {options.output_path}")
        
        logger.info("🎉 GUI转换测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"转换过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("GUI转换测试开始")
    logger.info("=" * 50)
    
    success = test_conversion()
    
    if success:
        logger.info("\n🎉 所有测试通过！")
        logger.info("现在可以运行run_gui.bat进行GUI测试")
    else:
        logger.error("\n❌ 测试失败")
        logger.info("请检查conversion_test.log文件查看详细错误信息")

if __name__ == "__main__":
    main()
