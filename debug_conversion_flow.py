# -*- coding: utf-8 -*-
"""
调试转换流程
"""

import sys
import os
from pathlib import Path

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def debug_conversion_steps():
    """调试转换的各个步骤"""
    
    univrm_vrm_path = r"I:\AIV\vrm2pmx-main\vrm-exam\Kuronyam 卫衣.vrm"
    
    if not os.path.exists(univrm_vrm_path):
        print(f"错误：找不到VRM文件 {univrm_vrm_path}")
        return False
    
    try:
        print("调试转换流程...")
        
        # 1. 读取VRM文件
        print("\n1. 读取VRM文件...")
        from mmd.VroidReader import VroidReader
        vroid_reader = VroidReader(univrm_vrm_path)
        vrm_model = vroid_reader.read_data()
        
        if not vrm_model or not vrm_model.json_data:
            print("  ✗ VRM文件读取失败")
            return False
        
        print(f"  ✓ VRM文件读取成功，节点数: {len(vrm_model.json_data.get('nodes', []))}")
        
        # 2. 检查关键数据结构
        print("\n2. 检查关键数据结构...")
        json_data = vrm_model.json_data
        
        # 检查extensions
        if "extensions" not in json_data:
            print("  ✗ 缺少extensions")
            return False
        print("  ✓ 找到extensions")
        
        # 检查VRM扩展
        if "VRM" not in json_data["extensions"]:
            print("  ✗ 缺少VRM扩展")
            return False
        print("  ✓ 找到VRM扩展")
        
        # 检查meta信息
        if "meta" not in json_data["extensions"]["VRM"]:
            print("  ✗ 缺少meta信息")
            return False
        print("  ✓ 找到meta信息")
        
        # 检查exporterVersion
        if "exporterVersion" not in json_data["extensions"]["VRM"]:
            print("  ✗ 缺少exporterVersion")
            return False
        
        exporter_version = json_data["extensions"]["VRM"]["exporterVersion"]
        print(f"  ✓ 找到exporterVersion: {exporter_version}")
        
        # 检查images
        if "images" not in json_data:
            print("  ✗ 缺少images信息")
            return False
        print(f"  ✓ 找到images信息，数量: {len(json_data['images'])}")
        
        # 检查bufferViews
        if "bufferViews" not in json_data:
            print("  ✗ 缺少bufferViews")
            return False
        print(f"  ✓ 找到bufferViews，数量: {len(json_data['bufferViews'])}")
        
        # 检查meshes
        if "meshes" not in json_data:
            print("  ✗ 缺少meshes")
            return False
        print(f"  ✓ 找到meshes，数量: {len(json_data['meshes'])}")
        
        # 检查accessors
        if "accessors" not in json_data:
            print("  ✗ 缺少accessors")
            return False
        print(f"  ✓ 找到accessors，数量: {len(json_data['accessors'])}")
        
        # 3. 尝试完整转换流程
        print("\n3. 尝试完整转换流程...")
        from service.VroidExportService import VroidExportService
        from module.MOptions import MOptionsDataSet

        options = MOptionsDataSet()
        options.vrm_model = vrm_model
        options.output_path = "debug_output.pmx"
        options.version_name = "debug_version"

        export_service = VroidExportService(options)

        try:
            # 步骤1: 创建基础模型
            print("  步骤1: 创建基础模型...")
            result = export_service.create_model()

            if result is None or (isinstance(result, tuple) and result[0] is None):
                print("    ✗ create_model返回None")
                return False

            if isinstance(result, tuple):
                pmx_model, tex_dir_path, setting_dir_path, is_vroid1 = result
                print(f"    ✓ create_model成功")
            else:
                pmx_model = result
                is_vroid1 = False
                tex_dir_path = None
                setting_dir_path = None

            print(f"      - 初始顶点数: {len(pmx_model.vertices)}")
            print(f"      - 初始骨骼数: {len(pmx_model.bones)}")
            print(f"      - 初始材质数: {len(pmx_model.materials)}")

            # 步骤2: 转换骨骼
            print("  步骤2: 转换骨骼...")
            pmx_model, bone_name_dict = export_service.convert_bone(pmx_model)
            if not pmx_model:
                print("    ✗ convert_bone失败")
                return False
            print(f"    ✓ convert_bone成功，骨骼数: {len(pmx_model.bones)}")

            # 步骤3: 转换网格
            print("  步骤3: 转换网格...")
            pmx_model = export_service.convert_mesh(pmx_model, bone_name_dict, tex_dir_path)
            if not pmx_model:
                print("    ✗ convert_mesh失败")
                return False
            print(f"    ✓ convert_mesh成功，顶点数: {len(pmx_model.vertices)}")

            # 步骤4: 重新转换骨骼
            print("  步骤4: 重新转换骨骼...")
            pmx_model = export_service.reconvert_bone(pmx_model)
            if not pmx_model:
                print("    ✗ reconvert_bone失败")
                return False
            print(f"    ✓ reconvert_bone成功")

            # 步骤5: 转换表情
            print("  步骤5: 转换表情...")
            pmx_model = export_service.convert_morph(pmx_model, is_vroid1)
            if not pmx_model:
                print("    ✗ convert_morph失败")
                return False
            print(f"    ✓ convert_morph成功")

            print(f"\n  最终结果:")
            print(f"    - 顶点数: {len(pmx_model.vertices)}")
            print(f"    - 骨骼数: {len(pmx_model.bones)}")
            print(f"    - 材质数: {len(pmx_model.materials)}")

            return True

        except Exception as e:
            print(f"  ✗ 转换失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_conversion_steps()
