#!/usr/bin/env python3
"""
清理VroidExportService.py文件，删除已移动到bone_pairs.py的BONE_PAIRS定义
"""

import re

def cleanup_vroid_service():
    """清理VroidExportService.py文件"""
    with open('src/service/VroidExportService.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到需要删除的范围
    start_idx = None
    end_idx = None
    
    for i, line in enumerate(lines):
        # 找到开始删除的位置 (从"Center": {开始)
        if '"Center": {' in line and start_idx is None:
            start_idx = i
        
        # 找到结束删除的位置 (UNIVRM_BONE_PAIRS的结束}之后)
        if start_idx is not None and line.strip() == '}' and i > start_idx + 100:
            # 检查下一行是否是空行或注释，表示BONE_PAIRS定义结束
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line == '' or next_line.startswith('#') or 'def get_bone_pairs' in next_line:
                    end_idx = i
                    break
    
    if start_idx is not None and end_idx is not None:
        print(f"删除第{start_idx + 1}行到第{end_idx + 1}行的内容")
        
        # 删除指定范围的行
        new_lines = lines[:start_idx] + lines[end_idx + 1:]
        
        # 写回文件
        with open('src/service/VroidExportService.py', 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print(f"成功删除{end_idx - start_idx + 1}行内容")
        print(f"文件从{len(lines)}行减少到{len(new_lines)}行")
    else:
        print("未找到需要删除的内容范围")
    
    # 删除重复的get_bone_pairs函数和BONE_PAIRS定义
    with open('src/service/VroidExportService.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 删除重复的get_bone_pairs函数定义
    pattern = r'# 根据VRM类型选择对应的BONE_PAIRS\ndef get_bone_pairs\(vrm_type\):.*?return VROID_BONE_PAIRS\n'
    content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 删除重复的BONE_PAIRS定义
    pattern = r'# 为了向后兼容，保持原有的BONE_PAIRS变量\nBONE_PAIRS = VROID_BONE_PAIRS\n'
    content = re.sub(pattern, '', content)
    
    with open('src/service/VroidExportService.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("清理完成！")

if __name__ == "__main__":
    cleanup_vroid_service()
