"""
VRM表情映射配置文件
包含VRoid Studio和UniVRM两种不同的表情命名规范的MORPH_PAIRS定义
"""

from module.MMath import MVector3D, MQuaternion

# 表情面板类型定义
MORPH_SYSTEM = 0
MORPH_EYEBROW = 1
MORPH_EYE = 2
MORPH_LIP = 3
MORPH_OTHER = 4

# VRoid Studio专用的MORPH_PAIRS
VROID_MORPH_PAIRS = {
    "Fcl_BRW_Fun_R": {"name": "にこり右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Fun"},
    "Fcl_BRW_Fun_L": {"name": "にこり左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Fun"},
    "Fcl_BRW_Fun": {"name": "にこり", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Joy_R": {"name": "にこり2右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Joy"},
    "Fcl_BRW_Joy_L": {"name": "にこり2左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Joy"},
    "Fcl_BRW_Joy": {"name": "にこり2", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Sorrow_R": {"name": "困る右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Sorrow"},
    "Fcl_BRW_Sorrow_L": {"name": "困る左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Sorrow"},
    "Fcl_BRW_Sorrow": {"name": "困る", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Angry_R": {"name": "怒り右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Angry"},
    "Fcl_BRW_Angry_L": {"name": "怒り左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Angry"},
    "Fcl_BRW_Angry": {"name": "怒り", "panel": MORPH_EYEBROW},
    "Fcl_BRW_Surprised_R": {"name": "驚き右", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Surprised"},
    "Fcl_BRW_Surprised_L": {"name": "驚き左", "panel": MORPH_EYEBROW, "split": "Fcl_BRW_Surprised"},
    "Fcl_BRW_Surprised": {"name": "驚き", "panel": MORPH_EYEBROW},
    "brow_Below_R": {"name": "下右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Below_L": {"name": "下左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Below": {"name": "下", "panel": MORPH_EYEBROW, "binds": ["brow_Below_R", "brow_Below_L"]},
    "brow_Abobe_R": {"name": "上右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Abobe_L": {"name": "上左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Abobe": {"name": "上", "panel": MORPH_EYEBROW, "binds": ["brow_Abobe_R", "brow_Abobe_L"]},
    "brow_Left_R": {"name": "右眉左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Left_L": {"name": "左眉左", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Left": {"name": "眉左", "panel": MORPH_EYEBROW, "binds": ["brow_Left_R", "brow_Left_L"]},
    "brow_Right_R": {"name": "右眉右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Right_L": {"name": "左眉右", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Right": {"name": "眉右", "panel": MORPH_EYEBROW, "binds": ["brow_Right_R", "brow_Right_L"]},
    "brow_Front_R": {"name": "右眉手前", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Front_L": {"name": "左眉手前", "panel": MORPH_EYEBROW, "creates": ["FaceBrow"]},
    "brow_Front": {"name": "眉手前", "panel": MORPH_EYEBROW, "binds": ["brow_Front_R", "brow_Front_L"]},
    "brow_Serious_R": {
        "name": "真面目右",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_R", "brow_Below_R"],
        "ratios": [0.25, 0.7],
    },
    "brow_Serious_L": {
        "name": "真面目左",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_L", "brow_Below_L"],
        "ratios": [0.25, 0.7],
    },
    "brow_Serious": {
        "name": "真面目",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_R", "brow_Below_R", "Fcl_BRW_Angry_L", "brow_Below_L"],
        "ratios": [0.25, 0.7, 0.25, 0.7],
    },
    "brow_Frown_R": {
        "name": "ひそめ右",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_R", "Fcl_BRW_Sorrow_R", "brow_Right_R"],
        "ratios": [0.5, 0.5, 0.3],
    },
    "brow_Frown_L": {
        "name": "ひそめ左",
        "panel": MORPH_EYEBROW,
        "binds": ["Fcl_BRW_Angry_L", "Fcl_BRW_Sorrow_L", "brow_Left_L"],
        "ratios": [0.5, 0.5, 0.3],
    },
    "brow_Frown": {
        "name": "ひそめ",
        "panel": MORPH_EYEBROW,
        "binds": [
            "Fcl_BRW_Angry_R",
            "Fcl_BRW_Sorrow_R",
            "brow_Right_R",
            "Fcl_BRW_Angry_L",
            "Fcl_BRW_Sorrow_L",
            "brow_Left_L",
        ],
        "ratios": [0.5, 0.5, 0.3, 0.5, 0.5, 0.3],
    },
    "browInnerUp_R": {"name": "ひそめる2右", "panel": MORPH_EYEBROW, "split": "browInnerUp"},
    "browInnerUp_L": {"name": "ひそめる2左", "panel": MORPH_EYEBROW, "split": "browInnerUp"},
    "browInnerUp": {"name": "ひそめる2", "panel": MORPH_EYEBROW},
    "browDownRight": {"name": "真面目2右", "panel": MORPH_EYEBROW},
    "browDownLeft": {"name": "真面目2左", "panel": MORPH_EYEBROW},
    "browDown": {"name": "真面目2", "panel": MORPH_EYEBROW, "binds": ["browDownRight", "browDownLeft"]},
    "browOuterUpRight": {"name": "はんっ右", "panel": MORPH_EYEBROW},
    "browOuterUpLeft": {"name": "はんっ左", "panel": MORPH_EYEBROW},
    "browOuter": {"name": "はんっ", "panel": MORPH_EYEBROW, "binds": ["browOuterUpRight", "browOuterUpLeft"]},
    "Fcl_EYE_Surprised_R": {"name": "びっくり右", "panel": MORPH_EYE, "split": "Fcl_EYE_Surprised"},
    "Fcl_EYE_Surprised_L": {"name": "びっくり左", "panel": MORPH_EYE, "split": "Fcl_EYE_Surprised"},
    "Fcl_EYE_Surprised": {"name": "びっくり", "panel": MORPH_EYE},
    "eye_Small_R": {"name": "瞳小右", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Small_L": {"name": "瞳小左", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Small": {"name": "瞳小", "panel": MORPH_EYE, "binds": ["eye_Small_R", "eye_Small_L"]},
    "eye_Big_R": {"name": "瞳大右", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Big_L": {"name": "瞳大左", "panel": MORPH_EYE, "creates": ["EyeIris", "EyeHighlight"]},
    "eye_Big": {"name": "瞳大", "panel": MORPH_EYE, "binds": ["eye_Big_R", "eye_Big_L"]},
    "Fcl_EYE_Close_R": {"name": "ｳｨﾝｸ２右", "panel": MORPH_EYE},
    "Fcl_EYE_Close_R_Bone": {
        "name": "ｳｨﾝｸ２右ボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "右目光",
        ],
        "move_ratios": [
            MVector3D(0, 0, -0.015),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-12, 0, 0),
        ],
    },
    "Fcl_EYE_Close_R_Group": {
        "name": "ｳｨﾝｸ２右連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_R",
            "Fcl_EYE_Close_R",
            "eye_Small_R",
            "Fcl_EYE_Close_R_Bone",
            "brow_Front_R",
            "Fcl_BRW_Sorrow_R",
        ],
        "ratios": [0.2, 1, 0.3, 1, 0.1, 0.2],
    },
    "Fcl_EYE_Close_L": {"name": "ウィンク２", "panel": MORPH_EYE},
    "Fcl_EYE_Close_L_Bone": {
        "name": "ウィンク２ボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "左目光",
        ],
        "move_ratios": [
            MVector3D(0, 0, -0.015),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-12, 0, 0),
        ],
    },
    "Fcl_EYE_Close_L_Group": {
        "name": "ウィンク２連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_L",
            "Fcl_EYE_Close_L",
            "eye_Small_L",
            "Fcl_EYE_Close_L_Bone",
            "brow_Front_L",
            "Fcl_BRW_Sorrow_L",
        ],
        "ratios": [0.2, 1, 0.3, 1, 0.1, 0.2],
    },
    "Fcl_EYE_Close": {"name": "まばたき", "panel": MORPH_EYE},
    "Fcl_EYE_Close_Group": {
        "name": "まばたき連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_R",
            "Fcl_EYE_Close_R",
            "eye_Small_R",
            "Fcl_EYE_Close_R_Bone",
            "brow_Front_R",
            "Fcl_BRW_Sorrow_R",
            "brow_Below_L",
            "Fcl_EYE_Close_L",
            "eye_Small_L",
            "Fcl_EYE_Close_L_Bone",
            "brow_Front_L",
            "Fcl_BRW_Sorrow_L",
        ],
        "ratios": [0.2, 1, 0.3, 1, 0.1, 0.2, 0.2, 1, 0.3, 1, 0.1, 0.2],
    },
    "Fcl_EYE_Joy_R": {"name": "ウィンク右", "panel": MORPH_EYE},
    "Fcl_EYE_Joy_R_Bone": {
        "name": "ウィンク右ボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "右目光",
        ],
        "move_ratios": [
            MVector3D(0, 0, 0.025),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(8, 0, 0),
        ],
    },
    "Fcl_EYE_Joy_R_Group": {
        "name": "ウィンク右連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_R",
            "Fcl_EYE_Joy_R",
            "eye_Small_R",
            "Fcl_EYE_Joy_R_Bone",
            "brow_Front_R",
            "Fcl_BRW_Fun_R",
        ],
        "ratios": [0.5, 1, 0.3, 1, 0.1, 0.5],
    },
    "Fcl_EYE_Joy_L": {"name": "ウィンク", "panel": MORPH_EYE},
    "Fcl_EYE_Joy_L_Bone": {
        "name": "ウィンクボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "左目光",
        ],
        "move_ratios": [
            MVector3D(0, 0, 0.025),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(8, 0, 0),
        ],
    },
    "Fcl_EYE_Joy_L_Group": {
        "name": "ウィンク連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_L",
            "Fcl_EYE_Joy_L",
            "eye_Small_L",
            "Fcl_EYE_Joy_L_Bone",
            "brow_Front_L",
            "Fcl_BRW_Fun_L",
        ],
        "ratios": [0.5, 1, 0.3, 1, 0.1, 0.5],
    },
    "Fcl_EYE_Joy": {"name": "笑い", "panel": MORPH_EYE},
    "Fcl_EYE_Joy_Group": {
        "name": "笑い連動",
        "panel": MORPH_EYE,
        "binds": [
            "brow_Below_R",
            "Fcl_EYE_Joy_R",
            "eye_Small_R",
            "Fcl_EYE_Joy_R_Bone",
            "brow_Front_R",
            "Fcl_BRW_Fun_R",
            "brow_Below_L",
            "Fcl_EYE_Joy_L",
            "eye_Small_L",
            "Fcl_EYE_Joy_L_Bone",
            "brow_Front_L",
            "Fcl_BRW_Fun_L",
        ],
        "ratios": [0.5, 1, 0.3, 1, 0.1, 0.5, 0.5, 1, 0.3, 1, 0.1, 0.5],
    },
    "Fcl_EYE_Fun_R": {"name": "目を細める右", "panel": MORPH_EYE, "split": "Fcl_EYE_Fun"},
    "Fcl_EYE_Fun_L": {"name": "目を細める左", "panel": MORPH_EYE, "split": "Fcl_EYE_Fun"},
    "Fcl_EYE_Fun": {"name": "目を細める", "panel": MORPH_EYE},
    "raiseEyelid_R": {"name": "下瞼上げ右", "panel": MORPH_EYE, "split": "Fcl_EYE_Fun_R"},
    "raiseEyelid_L": {"name": "下瞼上げ左", "panel": MORPH_EYE, "split": "Fcl_EYE_Fun_L"},
    "raiseEyelid": {"name": "下瞼上げ", "panel": MORPH_EYE, "binds": ["raiseEyelid_R", "raiseEyelid_L"]},
    "eyeSquintRight": {"name": "にんまり右", "panel": MORPH_EYE},
    "eyeSquintLeft": {"name": "にんまり左", "panel": MORPH_EYE},
    "eyeSquint": {"name": "にんまり", "panel": MORPH_EYE, "binds": ["eyeSquintRight", "eyeSquintLeft"]},
    "Fcl_EYE_Angry_R": {"name": "ｷﾘｯ右", "panel": MORPH_EYE, "split": "Fcl_EYE_Angry"},
    "Fcl_EYE_Angry_L": {"name": "ｷﾘｯ左", "panel": MORPH_EYE, "split": "Fcl_EYE_Angry"},
    "Fcl_EYE_Angry": {"name": "ｷﾘｯ", "panel": MORPH_EYE},
    "noseSneerRight": {"name": "ｷﾘｯ2右", "panel": MORPH_EYE},
    "noseSneerLeft": {"name": "ｷﾘｯ2左", "panel": MORPH_EYE},
    "noseSneer": {"name": "ｷﾘｯ2", "panel": MORPH_EYE, "binds": ["noseSneerRight", "noseSneerLeft"]},
    "Fcl_EYE_Sorrow_R": {"name": "じと目右", "panel": MORPH_EYE, "split": "Fcl_EYE_Sorrow"},
    "Fcl_EYE_Sorrow_L": {"name": "じと目左", "panel": MORPH_EYE, "split": "Fcl_EYE_Sorrow"},
    "Fcl_EYE_Sorrow": {"name": "じと目", "panel": MORPH_EYE},
    "Fcl_EYE_Spread_R": {"name": "上瞼↑右", "panel": MORPH_EYE, "split": "Fcl_EYE_Spread"},
    "Fcl_EYE_Spread_L": {"name": "上瞼↑左", "panel": MORPH_EYE, "split": "Fcl_EYE_Spread"},
    "Fcl_EYE_Spread": {"name": "上瞼↑", "panel": MORPH_EYE},
    "eye_Nanu_R": {
        "name": "なぬ！右",
        "panel": MORPH_EYE,
        "binds": ["Fcl_EYE_Surprised_R", "Fcl_EYE_Angry_R"],
        "ratios": [1, 1],
    },
    "eye_Nanu_L": {
        "name": "なぬ！左",
        "panel": MORPH_EYE,
        "binds": ["Fcl_EYE_Surprised_L", "Fcl_EYE_Angry_L"],
        "ratios": [1, 1],
    },
    "eye_Nanu": {
        "name": "なぬ！",
        "panel": MORPH_EYE,
        "binds": ["Fcl_EYE_Surprised_R", "Fcl_EYE_Angry_R", "Fcl_EYE_Surprised_L", "Fcl_EYE_Angry_L"],
        "ratios": [1, 1, 1, 1],
    },
    "eye_Hide_Vertex": {
        "name": "目隠し頂点",
        "panel": MORPH_SYSTEM,
        "creates": ["EyeWhite"],
        "hides": ["Eyeline", "Eyelash"],
    },
    "eye_Hau_Material": {
        "name": "はぅ材質",
        "panel": MORPH_SYSTEM,
        "material": "eye_hau",
        "hides": ["EyeWhite", "Eyeline", "Eyelash"],
    },
    "eye_Hau": {
        "name": "はぅ",
        "panel": MORPH_EYE,
        "binds": ["eye_Hau_Material", "eye_Hide_Vertex"],
    },
    "eye_Hachume_Material": {
        "name": "はちゅ目材質",
        "panel": MORPH_SYSTEM,
        "material": "eye_hachume",
        "hides": ["EyeWhite", "Eyeline", "Eyelash"],
    },
    "eye_Hachume": {
        "name": "はちゅ目",
        "panel": MORPH_EYE,
        "binds": ["eye_Hachume_Material", "eye_Hide_Vertex"],
    },
    "eye_Nagomi_Material": {
        "name": "なごみ材質",
        "panel": MORPH_SYSTEM,
        "material": "eye_nagomi",
        "hides": ["EyeWhite", "Eyeline", "Eyelash"],
    },
    "eye_Nagomi": {
        "name": "なごみ",
        "panel": MORPH_EYE,
        "binds": ["eye_Nagomi_Material", "eye_Hide_Vertex"],
    },
    "eye_Star_Material": {"name": "星目材質", "panel": MORPH_SYSTEM, "material": "eye_star"},
    "eye_Heart_Material": {"name": "はぁと材質", "panel": MORPH_SYSTEM, "material": "eye_heart"},
    "eye_Star": {"name": "星目", "panel": MORPH_EYE, "binds": ["Fcl_EYE_Highlight_Hide", "eye_Star_Material"]},
    "eye_Heart": {"name": "はぁと", "panel": MORPH_EYE, "binds": ["Fcl_EYE_Highlight_Hide", "eye_Heart_Material"]},
    "Fcl_EYE_Natural": {"name": "ナチュラル", "panel": MORPH_EYE},
    "eyeWideRight": {"name": "びっくり2右", "panel": MORPH_EYE},
    "eyeWideLeft": {"name": "びっくり2左", "panel": MORPH_EYE},
    "eyeWide": {"name": "びっくり2", "panel": MORPH_EYE, "binds": ["eyeSquintRight", "eyeSquintLeft"]},
    "eyeLookUpRight": {"name": "目上右", "panel": MORPH_EYE},
    "eyeLookUpLeft": {"name": "目上左", "panel": MORPH_EYE},
    "eyeLookUp": {"name": "目上", "panel": MORPH_EYE, "binds": ["eyeLookUpRight", "eyeLookUpLeft"]},
    "eyeLookDownRight": {"name": "目下右", "panel": MORPH_EYE},
    "eyeLookDownLeft": {"name": "目下左", "panel": MORPH_EYE},
    "eyeLookDown": {"name": "目下", "panel": MORPH_EYE, "binds": ["eyeLookDownRight", "eyeLookDownLeft"]},
    "eyeLookInRight": {"name": "目頭広右", "panel": MORPH_EYE},
    "eyeLookInLeft": {"name": "目頭広左", "panel": MORPH_EYE},
    "eyeLookIn": {"name": "目頭広", "panel": MORPH_EYE, "binds": ["eyeLookInRight", "eyeLookInLeft"]},
    "eyeLookOutLeft": {"name": "目尻広右", "panel": MORPH_EYE},
    "eyeLookOutRight": {"name": "目尻広左", "panel": MORPH_EYE},
    "eyeLookOut": {"name": "目尻広", "panel": MORPH_EYE, "binds": ["eyeLookOutRight", "eyeLookOutLeft"]},
    # "eyeBlinkLeft": {"name": "", "panel": MORPH_EYE},
    # "eyeBlinkRight": {"name": "", "panel": MORPH_EYE},
    "_eyeIrisMoveBack_R": {"name": "瞳小2右", "panel": MORPH_EYE},
    "_eyeIrisMoveBack_L": {"name": "瞳小2左", "panel": MORPH_EYE},
    "_eyeIrisMoveBack": {"name": "瞳小2", "panel": MORPH_EYE, "binds": ["_eyeIrisMoveBack_R", "_eyeIrisMoveBack_L"]},
    "_eyeSquint+LowerUp_R": {"name": "下瞼上げ2右", "panel": MORPH_EYE},
    "_eyeSquint+LowerUp_L": {"name": "下瞼上げ2左", "panel": MORPH_EYE},
    "_eyeSquint+LowerUp": {
        "name": "下瞼上げ2",
        "panel": MORPH_EYE,
        "binds": ["_eyeSquint+LowerUp_R", "_eyeSquint+LowerUp_L"],
    },
    "Fcl_EYE_Iris_Hide": {"name": "白目", "panel": MORPH_EYE},
    "Fcl_EYE_Iris_Hide_R": {"name": "白目右", "panel": MORPH_EYE, "split": "Fcl_EYE_Iris_Hide"},
    "Fcl_EYE_Iris_Hide_L": {"name": "白目左", "panel": MORPH_EYE, "split": "Fcl_EYE_Iris_Hide"},
    "Fcl_EYE_Highlight_Hide": {"name": "ハイライトなし", "panel": MORPH_EYE},
    "Fcl_EYE_Highlight_Hide_R": {"name": "ハイライトなし右", "panel": MORPH_EYE, "split": "Fcl_EYE_Highlight_Hide"},
    "Fcl_EYE_Highlight_Hide_L": {"name": "ハイライトなし左", "panel": MORPH_EYE, "split": "Fcl_EYE_Highlight_Hide"},
    "Fcl_MTH_A": {"name": "あ頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_A_Bone": {
        "name": "あボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-16, 0, 0),
            MQuaternion.fromEulerAngles(-16, 0, 0),
            MQuaternion.fromEulerAngles(-10, 0, 0),
        ],
    },
    "Fcl_MTH_A_Group": {"name": "あ", "panel": MORPH_LIP, "binds": ["Fcl_MTH_A", "Fcl_MTH_A_Bone"]},
    "Fcl_MTH_I": {"name": "い頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_I_Bone": {
        "name": "いボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-6, 0, 0),
            MQuaternion.fromEulerAngles(-6, 0, 0),
            MQuaternion.fromEulerAngles(-3, 0, 0),
        ],
    },
    "Fcl_MTH_I_Group": {"name": "い", "panel": MORPH_LIP, "binds": ["Fcl_MTH_I", "Fcl_MTH_I_Bone"]},
    "Fcl_MTH_U": {"name": "う頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_U_Bone": {
        "name": "うボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-16, 0, 0),
            MQuaternion.fromEulerAngles(-16, 0, 0),
            MQuaternion.fromEulerAngles(-10, 0, 0),
        ],
    },
    "Fcl_MTH_U_Group": {"name": "う", "panel": MORPH_LIP, "binds": ["Fcl_MTH_U", "Fcl_MTH_U_Bone"]},
    "Fcl_MTH_E": {"name": "え頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_E_Bone": {
        "name": "えボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-6, 0, 0),
            MQuaternion.fromEulerAngles(-6, 0, 0),
            MQuaternion.fromEulerAngles(-3, 0, 0),
        ],
    },
    "Fcl_MTH_E_Group": {"name": "え", "panel": MORPH_LIP, "binds": ["Fcl_MTH_E", "Fcl_MTH_E_Bone"]},
    "Fcl_MTH_O": {"name": "お頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_O_Bone": {
        "name": "おボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-20, 0, 0),
            MQuaternion.fromEulerAngles(-18, 0, 0),
            MQuaternion.fromEulerAngles(-12, 0, 0),
        ],
    },
    "Fcl_MTH_O_Group": {"name": "お", "panel": MORPH_LIP, "binds": ["Fcl_MTH_O", "Fcl_MTH_O_Bone"]},
    "Fcl_MTH_Neutral": {"name": "ん", "panel": MORPH_LIP},
    "Fcl_MTH_Close": {"name": "一文字", "panel": MORPH_LIP},
    "Fcl_MTH_Up": {"name": "口上", "panel": MORPH_LIP},
    "Fcl_MTH_Down": {"name": "口下", "panel": MORPH_LIP},
    "Fcl_MTH_Angry_R": {"name": "Λ右", "panel": MORPH_LIP, "split": "Fcl_MTH_Angry"},
    "Fcl_MTH_Angry_L": {"name": "Λ左", "panel": MORPH_LIP, "split": "Fcl_MTH_Angry"},
    "Fcl_MTH_Angry": {"name": "Λ", "panel": MORPH_LIP},
    "Fcl_MTH_Sage_R": {
        "name": "口角下げ右",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Angry_R", "Fcl_MTH_Large"],
        "ratios": [1, 0.5],
    },
    "Fcl_MTH_Sage_L": {
        "name": "口角下げ左",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Angry_L", "Fcl_MTH_Large"],
        "ratios": [1, 0.5],
    },
    "Fcl_MTH_Sage": {
        "name": "口角下げ",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Angry", "Fcl_MTH_Large"],
        "ratios": [1, 0.5],
    },
    "Fcl_MTH_Small": {"name": "うー", "panel": MORPH_LIP},
    "Fcl_MTH_Large": {"name": "口横広げ", "panel": MORPH_LIP},
    "Fcl_MTH_Fun_R": {"name": "にっこり右", "panel": MORPH_LIP, "split": "Fcl_MTH_Fun"},
    "Fcl_MTH_Fun_L": {"name": "にっこり左", "panel": MORPH_LIP, "split": "Fcl_MTH_Fun"},
    "Fcl_MTH_Fun": {"name": "にっこり", "panel": MORPH_LIP},
    "Fcl_MTH_Niko_R": {
        "name": "にこ右",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Fun_R", "Fcl_MTH_Large"],
        "ratios": [1, -0.3],
    },
    "Fcl_MTH_Niko_L": {
        "name": "にこ左",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Fun_L", "Fcl_MTH_Large"],
        "ratios": [1, -0.3],
    },
    "Fcl_MTH_Niko": {
        "name": "にこ",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Fun_R", "Fcl_MTH_Fun_L", "Fcl_MTH_Large"],
        "ratios": [0.5, 0.5, -0.3],
    },
    "Fcl_MTH_Joy": {"name": "ワ頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_Joy_Bone": {
        "name": "ワボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
            "舌4",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-24, 0, 0),
            MQuaternion.fromEulerAngles(-24, 0, 0),
            MQuaternion.fromEulerAngles(16, 0, 0),
            MQuaternion.fromEulerAngles(28, 0, 0),
        ],
    },
    "Fcl_MTH_Joy_Group": {"name": "ワ", "panel": MORPH_LIP, "binds": ["Fcl_MTH_Joy", "Fcl_MTH_Joy_Bone"]},
    "Fcl_MTH_Sorrow": {"name": "▲頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_Sorrow_Bone": {
        "name": "▲ボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-6, 0, 0),
            MQuaternion.fromEulerAngles(-6, 0, 0),
            MQuaternion.fromEulerAngles(-3, 0, 0),
        ],
    },
    "Fcl_MTH_Sorrow_Group": {"name": "▲", "panel": MORPH_LIP, "binds": ["Fcl_MTH_Sorrow", "Fcl_MTH_Sorrow_Bone"]},
    "Fcl_MTH_Surprised": {"name": "わー頂点", "panel": MORPH_SYSTEM},
    "Fcl_MTH_Surprised_Bone": {
        "name": "わーボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
            "舌4",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-24, 0, 0),
            MQuaternion.fromEulerAngles(-24, 0, 0),
            MQuaternion.fromEulerAngles(16, 0, 0),
            MQuaternion.fromEulerAngles(28, 0, 0),
        ],
    },
    "Fcl_MTH_Surprised_Group": {
        "name": "わー",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_Surprised", "Fcl_MTH_Surprised_Bone"],
    },
    "Fcl_MTH_tongueOut": {
        "name": "べーボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(0, 0, -0.24),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(-9, 0, 0),
            MQuaternion.fromEulerAngles(-13.2, 0, 0),
            MQuaternion.fromEulerAngles(-23.2, 0, 0),
        ],
    },
    "Fcl_MTH_tongueOut_Group": {
        "name": "べー",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_A", "Fcl_MTH_I", "Fcl_MTH_tongueOut"],
        "ratios": [0.12, 0.56, 1],
    },
    "Fcl_MTH_tongueUp": {
        "name": "ぺろりボーン",
        "panel": MORPH_SYSTEM,
        "bone": [
            "舌1",
            "舌2",
            "舌3",
            "舌4",
        ],
        "move_ratios": [
            MVector3D(),
            MVector3D(0, -0.03, -0.18),
            MVector3D(),
            MVector3D(),
        ],
        "rotate_ratios": [
            MQuaternion.fromEulerAngles(0, -5, 0),
            MQuaternion.fromEulerAngles(33, -16, -4),
            MQuaternion.fromEulerAngles(15, 3.6, -1),
            MQuaternion.fromEulerAngles(20, 0, 0),
        ],
    },
    "Fcl_MTH_tongueUp_Group": {
        "name": "ぺろり",
        "panel": MORPH_LIP,
        "binds": ["Fcl_MTH_A", "Fcl_MTH_Fun", "Fcl_MTH_tongueUp"],
        "ratios": [0.12, 0.54, 1],
    },
    "jawOpen": {"name": "あああ", "panel": MORPH_LIP},
    "jawForward": {"name": "顎前", "panel": MORPH_LIP},
    "jawLeft": {"name": "顎左", "panel": MORPH_LIP},
    "jawRight": {"name": "顎右", "panel": MORPH_LIP},
    "mouthFunnel": {"name": "んむー", "panel": MORPH_LIP},
    "mouthPucker": {"name": "うー", "panel": MORPH_LIP},
    "mouthLeft": {"name": "口左", "panel": MORPH_LIP},
    "mouthRight": {"name": "口右", "panel": MORPH_LIP},
    "mouthRollUpper": {"name": "上唇んむー", "panel": MORPH_LIP},
    "mouthRollLower": {"name": "下唇んむー", "panel": MORPH_LIP},
    "mouthRoll": {"name": "んむー", "panel": MORPH_LIP, "binds": ["mouthRollUpper", "mouthRollLower"]},
    "mouthShrugUpper": {"name": "上唇むむ", "panel": MORPH_LIP},
    "mouthShrugLower": {"name": "下唇むむ", "panel": MORPH_LIP},
    "mouthShrug": {"name": "むむ", "panel": MORPH_LIP, "binds": ["mouthShrugUpper", "mouthShrugLower"]},
    # "mouthClose": {"name": "", "panel": MORPH_LIP},
    "mouthDimpleRight": {"name": "口幅広右", "panel": MORPH_LIP},
    "mouthDimpleLeft": {"name": "口幅広左", "panel": MORPH_LIP},
    "mouthDimple": {"name": "口幅広", "panel": MORPH_LIP, "binds": ["mouthDimpleRight", "mouthDimpleLeft"]},
    "mouthPressRight": {"name": "薄笑い右", "panel": MORPH_LIP},
    "mouthPressLeft": {"name": "薄笑い左", "panel": MORPH_LIP},
    "mouthPress": {"name": "薄笑い", "panel": MORPH_LIP, "binds": ["mouthPressRight", "mouthPressLeft"]},
    "mouthSmileRight": {"name": "にやり2右", "panel": MORPH_LIP},
    "mouthSmileLeft": {"name": "にやり2左", "panel": MORPH_LIP},
    "mouthSmile": {"name": "にやり2", "panel": MORPH_LIP, "binds": ["mouthSmileRight", "mouthSmileLeft"]},
    "mouthUpperUpRight": {"name": "にひ右", "panel": MORPH_LIP},
    "mouthUpperUpLeft": {"name": "にひ左", "panel": MORPH_LIP},
    "mouthUpperUp": {"name": "にひ", "panel": MORPH_LIP, "binds": ["mouthUpperUpRight", "mouthDimpleLeft"]},
    "cheekSquintRight": {"name": "にひひ右", "panel": MORPH_LIP},
    "cheekSquintLeft": {"name": "にひひ左", "panel": MORPH_LIP},
    "cheekSquint": {"name": "にひひ", "panel": MORPH_LIP, "binds": ["cheekSquintRight", "cheekSquintLeft"]},
    "mouthFrownRight": {"name": "ちっ右", "panel": MORPH_LIP},
    "mouthFrownLeft": {"name": "ちっ左", "panel": MORPH_LIP},
    "mouthFrown": {"name": "ちっ", "panel": MORPH_LIP, "binds": ["mouthFrownRight", "mouthFrownLeft"]},
    "mouthLowerDownRight": {"name": "むっ右", "panel": MORPH_LIP},
    "mouthLowerDownLeft": {"name": "むっ左", "panel": MORPH_LIP},
    "mouthLowerDown": {"name": "むっ", "panel": MORPH_LIP, "binds": ["mouthLowerDownRight", "mouthLowerDownLeft"]},
    "mouthStretchRight": {"name": "ぎりっ右", "panel": MORPH_LIP},
    "mouthStretchLeft": {"name": "ぎりっ左", "panel": MORPH_LIP},
    "mouthStretch": {"name": "ぎりっ", "panel": MORPH_LIP, "binds": ["mouthStretchRight", "mouthStretchLeft"]},
    "tongueOut": {"name": "べー", "panel": MORPH_LIP},
    "_mouthFunnel+SharpenLips": {"name": "うほっ", "panel": MORPH_LIP},
    "_mouthPress+CatMouth": {"name": "ω口", "panel": MORPH_LIP},
    "_mouthPress+CatMouth-ex": {"name": "ω口2", "panel": MORPH_LIP},
    "_mouthPress+DuckMouth": {"name": "ω口3", "panel": MORPH_LIP},
    "cheekPuff_R": {"name": "ぷくー右", "panel": MORPH_LIP, "split": "cheekPuff"},
    "cheekPuff_L": {"name": "ぷくー左", "panel": MORPH_LIP, "split": "cheekPuff"},
    "cheekPuff": {"name": "ぷくー", "panel": MORPH_LIP},
    "Fcl_MTH_SkinFung_L": {"name": "肌牙左", "panel": MORPH_LIP},
    "Fcl_MTH_SkinFung_R": {"name": "肌牙右", "panel": MORPH_LIP},
    "Fcl_MTH_SkinFung": {"name": "肌牙", "panel": MORPH_LIP},
    "Fcl_HA_Fung1": {"name": "牙", "panel": MORPH_LIP},
    "Fcl_HA_Fung1_Up_R": {"name": "牙上右", "panel": MORPH_LIP, "split": "Fcl_HA_Fung1_Up"},
    "Fcl_HA_Fung1_Up_L": {"name": "牙上左", "panel": MORPH_LIP, "split": "Fcl_HA_Fung1_Up"},
    "Fcl_HA_Fung1_Up": {"name": "牙上", "panel": MORPH_LIP},
    "Fcl_HA_Fung1_Low_R": {"name": "牙下右", "panel": MORPH_LIP, "split": "Fcl_HA_Fung1_Low"},
    "Fcl_HA_Fung1_Low_L": {"name": "牙下左", "panel": MORPH_LIP, "split": "Fcl_HA_Fung1_Low"},
    "Fcl_HA_Fung1_Low": {"name": "牙下", "panel": MORPH_LIP},
    "Fcl_HA_Fung2_Up": {"name": "ギザ歯上", "panel": MORPH_LIP},
    "Fcl_HA_Fung2_Low": {"name": "ギザ歯下", "panel": MORPH_LIP},
    "Fcl_HA_Fung2": {"name": "ギザ歯", "panel": MORPH_LIP},
    "Fcl_HA_Fung3_Up": {"name": "真ん中牙上", "panel": MORPH_LIP},
    "Fcl_HA_Fung3_Low": {"name": "真ん中牙下", "panel": MORPH_LIP},
    "Fcl_HA_Fung3": {"name": "真ん中牙", "panel": MORPH_LIP},
    "Fcl_HA_Hide": {"name": "歯隠", "panel": MORPH_LIP},
    "Fcl_HA_Short_Up": {"name": "歯短上", "panel": MORPH_LIP},
    "Fcl_HA_Short_Low": {"name": "歯短下", "panel": MORPH_LIP},
    "Fcl_HA_Short": {"name": "歯短", "panel": MORPH_LIP},
    "Cheek_Dye": {"name": "照れ", "panel": MORPH_OTHER, "material": "cheek_dye"},
    "Fcl_ALL_Neutral": {"name": "ニュートラル", "panel": MORPH_OTHER},
    "Fcl_ALL_Angry": {"name": "怒", "panel": MORPH_OTHER},
    "Fcl_ALL_Fun": {"name": "楽", "panel": MORPH_OTHER},
    "Fcl_ALL_Joy": {"name": "喜", "panel": MORPH_OTHER},
    "Fcl_ALL_Sorrow": {"name": "哀", "panel": MORPH_OTHER},
    "Fcl_ALL_Surprised": {"name": "驚", "panel": MORPH_OTHER},
    "Edge_Off": {"name": "エッジOFF", "panel": MORPH_OTHER, "edge": True},
}

# UniVRM专用的MORPH_PAIRS (主要是标准VRM表情)
UNIVRM_MORPH_PAIRS = {
    # 基本VRM标准表情（两个平台都支持）
    "Neutral": {"name": "通常", "panel": MORPH_OTHER},
    "A": {"name": "あ", "panel": MORPH_LIP},
    "I": {"name": "い", "panel": MORPH_LIP},
    "U": {"name": "う", "panel": MORPH_LIP},
    "E": {"name": "え", "panel": MORPH_LIP},
    "O": {"name": "お", "panel": MORPH_LIP},
    "Blink": {"name": "まばたき", "panel": MORPH_EYE},
    "Joy": {"name": "喜び", "panel": MORPH_OTHER},
    "Angry": {"name": "怒り", "panel": MORPH_OTHER},
    "Sorrow": {"name": "悲しみ", "panel": MORPH_OTHER},
    "Fun": {"name": "楽しい", "panel": MORPH_OTHER},
    "LookUp": {"name": "見上げ", "panel": MORPH_EYE},
    "LookDown": {"name": "見下げ", "panel": MORPH_EYE},
    "LookLeft": {"name": "見左", "panel": MORPH_EYE},
    "LookRight": {"name": "見右", "panel": MORPH_EYE},
    "Blink_L": {"name": "ウィンク", "panel": MORPH_EYE},
    "Blink_R": {"name": "ウィンク２", "panel": MORPH_EYE},
    "Surprised": {"name": "驚き", "panel": MORPH_OTHER},
    
    # 一些可能存在的基本表情变体
    "blink": {"name": "まばたき", "panel": MORPH_EYE},  # 小写版本
    "joy": {"name": "喜び", "panel": MORPH_OTHER},
    "angry": {"name": "怒り", "panel": MORPH_OTHER},
    "sorrow": {"name": "悲しみ", "panel": MORPH_OTHER},
    "fun": {"name": "楽しい", "panel": MORPH_OTHER},
    "surprised": {"name": "驚き", "panel": MORPH_OTHER},
}

# 根据VRM类型选择对应的MORPH_PAIRS
def get_morph_pairs(vrm_type):
    """根据VRM导出器类型返回对应的MORPH_PAIRS"""
    if vrm_type == "UniVRM":
        return UNIVRM_MORPH_PAIRS
    else:
        return VROID_MORPH_PAIRS

# 为了向后兼容，保持原有的MORPH_PAIRS变量
MORPH_PAIRS = VROID_MORPH_PAIRS
