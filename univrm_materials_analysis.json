{"total_count": 19, "materials": [{"index": 0, "name": "Mat_NYM_Skin", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 0, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 1, "name": "Mat_NYM_AccessoryBoots", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 1, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 2, "scale": 0.5, "texCoord": 0}, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 2, "name": "Mat_NYM_Accessory", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 1, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 2, "scale": 0.5, "texCoord": 0}, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 3, "name": "<PERSON>_<PERSON><PERSON>_Hair", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 3, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 4, "scale": 1, "texCoord": 0}, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 4, "name": "<PERSON>_<PERSON>M_Blow", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 5, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 5, "name": "<PERSON>_<PERSON>M_Face", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 5, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 6, "name": "<PERSON>_<PERSON><PERSON>_Face_Transparent", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 5, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "MASK", "alpha_cutoff": 0.001, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 7, "name": "Mat_NYM_Eye", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 6, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 7, "texCoord": 0}, "emissive_factor": [0, 0, 0], "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 8, "name": "Mat_NYM_EyeHighLight", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 6, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 7, "texCoord": 0}, "emissive_factor": [0, 0, 0], "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 9, "name": "<PERSON>_<PERSON>M_FacialEff_Face", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 8, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 9, "texCoord": 0}, "emissive_factor": [0, 0, 0], "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 10, "name": "<PERSON>_NYM_FacialEff_Eye", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 8, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 9, "texCoord": 0}, "emissive_factor": [0, 0, 0], "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 11, "name": "<PERSON>_NYM_FacialEff_Head", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 8, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 12, "name": "Mat_NYM_FacialEff_Multi", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 8, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 9, "texCoord": 0}, "emissive_factor": [0, 0, 0], "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 13, "name": "<PERSON>_NYM_FacialEff_Other", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 8, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 14, "name": "<PERSON>_<PERSON><PERSON>_Hair_Shadow", "pbr_metallic_roughness": {"baseColorFactor": [1, 0.5520115, 0.61772573, 1], "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 15, "name": "<PERSON>_NYM_Wear_B", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 10, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 11, "scale": 1, "texCoord": 0}, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 16, "name": "Mat_NYM_Wear_A", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 12, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 13, "scale": 1, "texCoord": 0}, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 17, "name": "<PERSON>_<PERSON><PERSON>_Doll", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 14, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 15, "scale": 1, "texCoord": 0}, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "OPAQUE", "alpha_cutoff": null, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}, {"index": 18, "name": "Mat_NYM_Wear_A_Transparent", "pbr_metallic_roughness": {"baseColorFactor": [1, 1, 1, 1], "baseColorTexture": {"extensions": {"KHR_texture_transform": {"offset": [0, 0], "scale": [1, 1]}}, "index": 12, "texCoord": 0}, "metallicFactor": 0, "roughnessFactor": 0.9}, "normal_texture": null, "occlusion_texture": null, "emissive_texture": null, "emissive_factor": null, "alpha_mode": "MASK", "alpha_cutoff": 0.5, "double_sided": true, "extensions": {"KHR_materials_unlit": {}}}]}