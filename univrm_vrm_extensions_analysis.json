{"exporter_version": "UniVRM-0.99.4", "spec_version": "0.0", "meta": {"allowedUserName": "Only<PERSON><PERSON><PERSON>", "author": "闲鱼 请将我深埋 制作", "commercialUssageName": "Disallow", "licenseName": "Redistribution_Prohibited", "sexualUssageName": "Disallow", "texture": 16, "title": "Kuronyam 卫衣", "version": "仅 请将我深埋&追居实录&小小播播姬 出售 其它均为盗版倒卖 禁止直播使用 禁止二转", "violentUssageName": "Disallow"}, "humanoid": {"armStretch": 0.05, "feetSpacing": 0, "hasTranslationDoF": false, "humanBones": [{"bone": "hips", "node": 1, "useDefaultValues": true}, {"bone": "leftUpperLeg", "node": 238, "useDefaultValues": true}, {"bone": "rightUpperLeg", "node": 247, "useDefaultValues": true}, {"bone": "leftLowerLeg", "node": 239, "useDefaultValues": true}, {"bone": "rightLowerLeg", "node": 248, "useDefaultValues": true}, {"bone": "leftFoot", "node": 240, "useDefaultValues": true}, {"bone": "rightFoot", "node": 249, "useDefaultValues": true}, {"bone": "spine", "node": 22, "useDefaultValues": true}, {"bone": "neck", "node": 45, "useDefaultValues": true}, {"bone": "head", "node": 46, "useDefaultValues": true}, {"bone": "leftShoulder", "node": 152, "useDefaultValues": true}, {"bone": "rightShoulder", "node": 192, "useDefaultValues": true}, {"bone": "leftUpperArm", "node": 153, "useDefaultValues": true}, {"bone": "rightUpperArm", "node": 193, "useDefaultValues": true}, {"bone": "leftLowerArm", "node": 154, "useDefaultValues": true}, {"bone": "rightLowerArm", "node": 194, "useDefaultValues": true}, {"bone": "leftHand", "node": 155, "useDefaultValues": true}, {"bone": "rightHand", "node": 195, "useDefaultValues": true}, {"bone": "leftToes", "node": 241, "useDefaultValues": true}, {"bone": "rightToes", "node": 250, "useDefaultValues": true}, {"bone": "leftEye", "node": 147, "useDefaultValues": true}, {"bone": "rightEye", "node": 148, "useDefaultValues": true}, {"bone": "leftThumbProximal", "node": 173, "useDefaultValues": true}, {"bone": "leftThumbIntermediate", "node": 174, "useDefaultValues": true}, {"bone": "leftThumbDistal", "node": 175, "useDefaultValues": true}, {"bone": "leftIndexProximal", "node": 157, "useDefaultValues": true}, {"bone": "leftIndexIntermediate", "node": 158, "useDefaultValues": true}, {"bone": "leftIndexDistal", "node": 159, "useDefaultValues": true}, {"bone": "leftMiddleProximal", "node": 165, "useDefaultValues": true}, {"bone": "leftMiddleIntermediate", "node": 166, "useDefaultValues": true}, {"bone": "leftMiddleDistal", "node": 167, "useDefaultValues": true}, {"bone": "leftRingProximal", "node": 169, "useDefaultValues": true}, {"bone": "leftRingIntermediate", "node": 170, "useDefaultValues": true}, {"bone": "leftRingDistal", "node": 171, "useDefaultValues": true}, {"bone": "leftLittleProximal", "node": 161, "useDefaultValues": true}, {"bone": "leftLittleIntermediate", "node": 162, "useDefaultValues": true}, {"bone": "leftLittleDistal", "node": 163, "useDefaultValues": true}, {"bone": "rightThumbProximal", "node": 213, "useDefaultValues": true}, {"bone": "rightThumbIntermediate", "node": 214, "useDefaultValues": true}, {"bone": "rightThumbDistal", "node": 215, "useDefaultValues": true}, {"bone": "rightIndexProximal", "node": 197, "useDefaultValues": true}, {"bone": "rightIndexIntermediate", "node": 198, "useDefaultValues": true}, {"bone": "rightIndexDistal", "node": 199, "useDefaultValues": true}, {"bone": "rightMiddleProximal", "node": 205, "useDefaultValues": true}, {"bone": "rightMiddleIntermediate", "node": 206, "useDefaultValues": true}, {"bone": "rightMiddleDistal", "node": 207, "useDefaultValues": true}, {"bone": "rightRingProximal", "node": 209, "useDefaultValues": true}, {"bone": "rightRingIntermediate", "node": 210, "useDefaultValues": true}, {"bone": "rightRingDistal", "node": 211, "useDefaultValues": true}, {"bone": "rightLittleProximal", "node": 201, "useDefaultValues": true}, {"bone": "rightLittleIntermediate", "node": 202, "useDefaultValues": true}, {"bone": "rightLittleDistal", "node": 203, "useDefaultValues": true}, {"bone": "chest", "node": 23, "useDefaultValues": true}], "legStretch": 0.05, "lowerArmTwist": 0.5, "lowerLegTwist": 0.5, "upperArmTwist": 0.5, "upperLegTwist": 0.5}, "blend_shape_master": {"blendShapeGroups": [{"binds": [], "isBinary": false, "materialValues": [], "name": "Neutral", "presetName": "neutral"}, {"binds": [{"index": 0, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "A", "presetName": "a"}, {"binds": [{"index": 5, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "I", "presetName": "i"}, {"binds": [{"index": 9, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "U", "presetName": "u"}, {"binds": [{"index": 3, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "E", "presetName": "e"}, {"binds": [{"index": 8, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "O", "presetName": "o"}, {"binds": [{"index": 49, "mesh": 4, "weight": 100}, {"index": 50, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "Blink", "presetName": "blink"}, {"binds": [{"index": 54, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "<PERSON>", "presetName": "joy"}, {"binds": [{"index": 23, "mesh": 4, "weight": 100}, {"index": 24, "mesh": 4, "weight": 100}, {"index": 41, "mesh": 4, "weight": 100}, {"index": 42, "mesh": 4, "weight": 100}, {"index": 66, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "Angry", "presetName": "angry"}, {"binds": [{"index": 25, "mesh": 4, "weight": 100}, {"index": 26, "mesh": 4, "weight": 100}, {"index": 43, "mesh": 4, "weight": 100}, {"index": 44, "mesh": 4, "weight": 100}, {"index": 102, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "Sorrow", "presetName": "sorrow"}, {"binds": [{"index": 77, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "Fun", "presetName": "fun"}, {"binds": [{"index": 61, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "LookUp", "presetName": "lookup"}, {"binds": [{"index": 62, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "LookDown", "presetName": "lookdown"}, {"binds": [{"index": 64, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "LookLeft", "presetName": "lookleft"}, {"binds": [{"index": 63, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "LookRight", "presetName": "lookright"}, {"binds": [{"index": 49, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "Blink_L", "presetName": "blink_l"}, {"binds": [{"index": 50, "mesh": 4, "weight": 100}], "isBinary": false, "materialValues": [], "name": "Blink_R", "presetName": "blink_r"}]}, "secondary_animation": {"boneGroups": [{"bones": [52, 57, 62, 67, 72, 77, 82, 87, 92, 97, 102, 107, 112, 117, 119, 121, 123, 126, 129, 134, 139, 143], "center": -1, "colliderGroups": [], "dragForce": 0.877, "gravityDir": {"x": 0, "y": -1, "z": 0}, "gravityPower": 0, "hitRadius": 0.02, "stiffiness": 1.3}, {"bones": [47, 49], "center": -1, "colliderGroups": [], "dragForce": 0.823, "gravityDir": {"x": 0, "y": -1, "z": 0}, "gravityPower": 0, "hitRadius": 0.02, "stiffiness": 1.38}, {"bones": [28, 33], "center": -1, "colliderGroups": [], "dragForce": 0.877, "gravityDir": {"x": 0, "y": -1, "z": 0}, "gravityPower": 0, "hitRadius": 0.02, "stiffiness": 1.49}, {"bones": [178, 182, 186, 188, 218, 222, 226, 228], "center": -1, "colliderGroups": [], "dragForce": 0.884, "gravityDir": {"x": 0, "y": -1, "z": 0}, "gravityPower": 0, "hitRadius": 0.02, "stiffiness": 1.22}, {"bones": [7], "center": -1, "colliderGroups": [], "dragForce": 0.905, "gravityDir": {"x": 0, "y": -1, "z": 0}, "gravityPower": 0, "hitRadius": 0.02, "stiffiness": 0.95}], "colliderGroups": []}, "material_properties": [{"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {}, "name": "Mat_NYM_Skin", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_MainTex": 0, "_ShadeTexture": 0}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 0.5, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_NORMALMAP": true}, "name": "Mat_NYM_AccessoryBoots", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_BumpMap": 2, "_MainTex": 1, "_ShadeTexture": 1}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 0.5, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_NORMALMAP": true}, "name": "Mat_NYM_Accessory", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_BumpMap": 2, "_MainTex": 1, "_ShadeTexture": 1}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_NORMALMAP": true}, "name": "<PERSON>_<PERSON><PERSON>_Hair", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_BumpMap": 4, "_MainTex": 3, "_ShadeTexture": 3}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {}, "name": "<PERSON>_<PERSON>M_Blow", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_MainTex": 5, "_ShadeTexture": 5}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {}, "name": "<PERSON>_<PERSON>M_Face", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_MainTex": 5, "_ShadeTexture": 5}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_ALPHATEST_ON": true}, "name": "<PERSON>_<PERSON><PERSON>_Face_Transparent", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_MainTex": 5, "_ShadeTexture": 5}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_EMISSION": true}, "name": "Mat_NYM_Eye", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_EmissionMap": 7, "_MainTex": 6, "_ShadeTexture": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_EMISSION": true}, "name": "Mat_NYM_EyeHighLight", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_EmissionMap": 7, "_MainTex": 6, "_ShadeTexture": 6}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_EMISSION": true}, "name": "<PERSON>_<PERSON>M_FacialEff_Face", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_EmissionMap": 9, "_MainTex": 8, "_ShadeTexture": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_EMISSION": true}, "name": "<PERSON>_NYM_FacialEff_Eye", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_EmissionMap": 9, "_MainTex": 8, "_ShadeTexture": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {}, "name": "<PERSON>_NYM_FacialEff_Head", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_MainTex": 8, "_ShadeTexture": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_EMISSION": true}, "name": "Mat_NYM_FacialEff_Multi", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_EmissionMap": 9, "_MainTex": 8, "_ShadeTexture": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {}, "name": "<PERSON>_NYM_FacialEff_Other", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_MainTex": 8, "_ShadeTexture": 8}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.001, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {}, "name": "<PERSON>_<PERSON><PERSON>_Hair_Shadow", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 0.768627465, 0.8081454, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_NORMALMAP": true}, "name": "<PERSON>_NYM_Wear_B", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_BumpMap": 11, "_MainTex": 10, "_ShadeTexture": 10}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_NORMALMAP": true}, "name": "Mat_NYM_Wear_A", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_BumpMap": 13, "_MainTex": 12, "_ShadeTexture": 12}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 0, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_NORMALMAP": true}, "name": "<PERSON>_<PERSON><PERSON>_Doll", "renderQueue": 2000, "shader": "VRM/MToon", "tagMap": {"RenderType": "Opaque"}, "textureProperties": {"_BumpMap": 15, "_MainTex": 14, "_ShadeTexture": 14}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}, {"floatProperties": {"_BlendMode": 1, "_BumpScale": 1, "_CullMode": 0, "_Cutoff": 0.5, "_DebugMode": 0, "_DstBlend": 0, "_IndirectLightIntensity": 0.1, "_LightColorAttenuation": 0, "_MToonVersion": 38, "_OutlineColorMode": 0, "_OutlineCullMode": 1, "_OutlineLightingMix": 1, "_OutlineScaledMaxDistance": 1, "_OutlineWidth": 0.5, "_OutlineWidthMode": 0, "_ReceiveShadowRate": 1, "_RimFresnelPower": 1, "_RimLift": 0, "_RimLightingMix": 0, "_ShadeShift": 0, "_ShadeToony": 0.9, "_ShadingGradeRate": 1, "_SrcBlend": 1, "_UvAnimRotation": 0, "_UvAnimScrollX": 0, "_UvAnimScrollY": 0, "_ZWrite": 1}, "keywordMap": {"_ALPHATEST_ON": true}, "name": "Mat_NYM_Wear_A_Transparent", "renderQueue": 2450, "shader": "VRM/MToon", "tagMap": {"RenderType": "TransparentCutout"}, "textureProperties": {"_MainTex": 12, "_ShadeTexture": 12}, "vectorProperties": {"_BumpMap": [0, 0, 1, 1], "_Color": [1, 1, 1, 1], "_EmissionColor": [0, 0, 0, 1], "_EmissionMap": [0, 0, 1, 1], "_MainTex": [0, 0, 1, 1], "_OutlineColor": [0, 0, 0, 1], "_OutlineWidthTexture": [0, 0, 1, 1], "_ReceiveShadowTexture": [0, 0, 1, 1], "_RimColor": [0, 0, 0, 1], "_RimTexture": [0, 0, 1, 1], "_ShadeColor": [1, 1, 1, 1], "_ShadeTexture": [0, 0, 1, 1], "_ShadingGradeTexture": [0, 0, 1, 1], "_SphereAdd": [0, 0, 1, 1], "_UvAnimMaskTexture": [0, 0, 1, 1]}}]}